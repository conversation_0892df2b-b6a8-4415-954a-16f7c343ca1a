import AsyncStorage from '@react-native-async-storage/async-storage';
import { BookmarkedQuestion, Question } from './DataManager';

class BookmarkManager {
  private static instance: BookmarkManager;
  private readonly STORAGE_KEY = 'bookmarked_questions';

  public static getInstance(): BookmarkManager {
    if (!BookmarkManager.instance) {
      BookmarkManager.instance = new BookmarkManager();
    }
    return BookmarkManager.instance;
  }

  /**
   * Generate a unique ID for a bookmark based on question content and source
   */
  private generateBookmarkId(question: Question, fileUrl: string): string {
    return `${fileUrl}_${question.question_number}_${question.question_text.slice(0, 50)}`.replace(/[^a-zA-Z0-9]/g, '_');
  }

  /**
   * Add a question to bookmarks
   */
  async addBookmark(question: Question, bcsTitle: string, fileUrl: string): Promise<void> {
    try {
      const bookmarks = await this.getBookmarks();
      const bookmarkId = this.generateBookmarkId(question, fileUrl);
      
      // Check if already bookmarked
      const existingIndex = bookmarks.findIndex(b => b.id === bookmarkId);
      if (existingIndex !== -1) {
        return; // Already bookmarked
      }

      const bookmarkedQuestion: BookmarkedQuestion = {
        ...question,
        id: bookmarkId,
        bcsTitle,
        fileUrl,
        bookmarkedAt: new Date().toISOString()
      };

      bookmarks.push(bookmarkedQuestion);
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(bookmarks));

      console.log('✅ Question bookmarked successfully:', {
        bookmarkId,
        totalBookmarks: bookmarks.length,
        bookmarkedQuestion
      });
    } catch (error) {
      console.error('Error adding bookmark:', error);
      throw new Error('Failed to bookmark question');
    }
  }

  /**
   * Remove a question from bookmarks
   */
  async removeBookmark(question: Question, fileUrl: string): Promise<void> {
    try {
      const bookmarks = await this.getBookmarks();
      const bookmarkId = this.generateBookmarkId(question, fileUrl);
      
      const filteredBookmarks = bookmarks.filter(b => b.id !== bookmarkId);
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredBookmarks));
      
      console.log('✅ Bookmark removed successfully');
    } catch (error) {
      console.error('Error removing bookmark:', error);
      throw new Error('Failed to remove bookmark');
    }
  }

  /**
   * Check if a question is bookmarked
   */
  async isBookmarked(question: Question, fileUrl: string): Promise<boolean> {
    try {
      const bookmarks = await this.getBookmarks();
      const bookmarkId = this.generateBookmarkId(question, fileUrl);
      return bookmarks.some(b => b.id === bookmarkId);
    } catch (error) {
      console.error('Error checking bookmark status:', error);
      return false;
    }
  }

  /**
   * Get all bookmarked questions
   */
  async getBookmarks(): Promise<BookmarkedQuestion[]> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      console.log('📱 AsyncStorage raw data:', stored);

      const result = stored ? JSON.parse(stored) : [];
      console.log('📱 Parsed bookmarks:', result.length, result);

      return result;
    } catch (error) {
      console.error('Error getting bookmarks:', error);
      return [];
    }
  }

  /**
   * Search bookmarks by text
   */
  async searchBookmarks(query: string): Promise<BookmarkedQuestion[]> {
    try {
      const bookmarks = await this.getBookmarks();
      const lowercaseQuery = query.toLowerCase();
      
      return bookmarks.filter(bookmark => 
        bookmark.question_text.toLowerCase().includes(lowercaseQuery) ||
        bookmark.subject.toLowerCase().includes(lowercaseQuery) ||
        bookmark.bcsTitle.toLowerCase().includes(lowercaseQuery) ||
        Object.values(bookmark.options).some(option => 
          option.toLowerCase().includes(lowercaseQuery)
        )
      );
    } catch (error) {
      console.error('Error searching bookmarks:', error);
      return [];
    }
  }

  /**
   * Filter bookmarks by subject
   */
  async getBookmarksBySubject(subject: string): Promise<BookmarkedQuestion[]> {
    try {
      const bookmarks = await this.getBookmarks();
      if (subject === 'All') return bookmarks;
      return bookmarks.filter(bookmark => bookmark.subject === subject);
    } catch (error) {
      console.error('Error filtering bookmarks by subject:', error);
      return [];
    }
  }

  /**
   * Filter bookmarks by BCS title
   */
  async getBookmarksByBCS(bcsTitle: string): Promise<BookmarkedQuestion[]> {
    try {
      const bookmarks = await this.getBookmarks();
      if (bcsTitle === 'All') return bookmarks;
      return bookmarks.filter(bookmark => bookmark.bcsTitle === bcsTitle);
    } catch (error) {
      console.error('Error filtering bookmarks by BCS:', error);
      return [];
    }
  }

  /**
   * Get unique subjects from bookmarks
   */
  async getBookmarkSubjects(): Promise<string[]> {
    try {
      const bookmarks = await this.getBookmarks();
      const subjects = [...new Set(bookmarks.map(b => b.subject))].filter(Boolean);
      return ['All', ...subjects.sort()];
    } catch (error) {
      console.error('Error getting bookmark subjects:', error);
      return ['All'];
    }
  }

  /**
   * Get unique BCS titles from bookmarks
   */
  async getBookmarkBCSTitles(): Promise<string[]> {
    try {
      const bookmarks = await this.getBookmarks();
      const bcsTitles = [...new Set(bookmarks.map(b => b.bcsTitle))].filter(Boolean);
      return ['All', ...bcsTitles.sort()];
    } catch (error) {
      console.error('Error getting bookmark BCS titles:', error);
      return ['All'];
    }
  }

  /**
   * Get bookmark statistics
   */
  async getBookmarkStats(): Promise<{
    total: number;
    bySubject: { [subject: string]: number };
    byBCS: { [bcsTitle: string]: number };
  }> {
    try {
      const bookmarks = await this.getBookmarks();
      
      const bySubject: { [subject: string]: number } = {};
      const byBCS: { [bcsTitle: string]: number } = {};
      
      bookmarks.forEach(bookmark => {
        bySubject[bookmark.subject] = (bySubject[bookmark.subject] || 0) + 1;
        byBCS[bookmark.bcsTitle] = (byBCS[bookmark.bcsTitle] || 0) + 1;
      });

      return {
        total: bookmarks.length,
        bySubject,
        byBCS
      };
    } catch (error) {
      console.error('Error getting bookmark stats:', error);
      return { total: 0, bySubject: {}, byBCS: {} };
    }
  }

  /**
   * Clear all bookmarks
   */
  async clearAllBookmarks(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      console.log('✅ All bookmarks cleared');
    } catch (error) {
      console.error('Error clearing bookmarks:', error);
      throw new Error('Failed to clear bookmarks');
    }
  }

  /**
   * Export bookmarks as JSON string
   */
  async exportBookmarks(): Promise<string> {
    try {
      const bookmarks = await this.getBookmarks();
      return JSON.stringify(bookmarks, null, 2);
    } catch (error) {
      console.error('Error exporting bookmarks:', error);
      throw new Error('Failed to export bookmarks');
    }
  }

  /**
   * Import bookmarks from JSON string
   */
  async importBookmarks(jsonData: string, mergeWithExisting = true): Promise<void> {
    try {
      const importedBookmarks: BookmarkedQuestion[] = JSON.parse(jsonData);
      
      if (!Array.isArray(importedBookmarks)) {
        throw new Error('Invalid bookmark data format');
      }

      let finalBookmarks = importedBookmarks;
      
      if (mergeWithExisting) {
        const existingBookmarks = await this.getBookmarks();
        const existingIds = new Set(existingBookmarks.map(b => b.id));
        
        // Only add bookmarks that don't already exist
        const newBookmarks = importedBookmarks.filter(b => !existingIds.has(b.id));
        finalBookmarks = [...existingBookmarks, ...newBookmarks];
      }

      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(finalBookmarks));
      console.log('✅ Bookmarks imported successfully');
    } catch (error) {
      console.error('Error importing bookmarks:', error);
      throw new Error('Failed to import bookmarks');
    }
  }
}

export default BookmarkManager;
