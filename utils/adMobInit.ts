export const initializeAdMob = async () => {
  try {
    const mobileAdsModule = require('react-native-google-mobile-ads');
    const mobileAds = mobileAdsModule.default || mobileAdsModule;

    if (mobileAds && typeof mobileAds.initialize === 'function') {
      await mobileAds.initialize();
      console.log('AdMob initialized successfully');
    } else if (mobileAds && typeof mobileAds === 'function') {
      await mobileAds().initialize();
      console.log('AdMob initialized successfully');
    } else {
      console.error('AdMob module not found or invalid');
    }
  } catch (error) {
    console.error('AdMob initialization failed:', error);
  }
};
