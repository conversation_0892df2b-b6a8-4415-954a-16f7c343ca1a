# Google Mobile Ads Setup Guide

## Overview
This guide explains how to set up and use Google Mobile Ads in your BCS Question Bank app for Android.

## Current Setup Status
✅ **Completed:**
- Google Mobile Ads package installed (`react-native-google-mobile-ads@15.4.0`)
- Android App ID configured in `app.json`
- AdMob initialization added to app root
- Banner ad component created and implemented
- Interstitial ad component created and implemented
- Rewarded ad component created

## Configuration

### 1. App ID Configuration
Your Android App ID is configured in `app.json`:
```json
"androidAppId": "ca-app-pub-****************~**********"
```

### 2. Ad Unit IDs
You need to create ad units in your AdMob console and replace the placeholder IDs:

**Current placeholders in components:**
- Banner Ad: `ca-app-pub-****************/XXXXXXXXXX`
- Interstitial Ad: `ca-app-pub-****************/XXXXXXXXXX`
- Rewarded Ad: `ca-app-pub-****************/XXXXXXXXXX`

**To get your Ad Unit IDs:**
1. Go to [AdMob Console](https://apps.admob.com/)
2. Select your app
3. Go to "Ad units" section
4. Create new ad units for each type (Banner, Interstitial, Rewarded)
5. Copy the Ad Unit IDs and replace the placeholders

## Implementation Examples

### Banner Ads
Banner ads are already implemented on the home screen:

```tsx
import { BannerAdComponent } from '@/components/ads';

// In your component
<BannerAdComponent style={styles.bannerAd} />
```

### Interstitial Ads
Interstitial ads are implemented in the questions screen and show every 5 questions:

```tsx
import { useInterstitialAd } from '@/components/ads';

// In your component
const { showAd, isLoaded } = useInterstitialAd();

// Show ad when needed
if (isLoaded) {
  showAd();
}
```

### Rewarded Ads
Example usage for rewarded ads:

```tsx
import { useRewardedAd } from '@/components/ads';

const { showAd, isLoaded, reward, clearReward } = useRewardedAd();

// Show rewarded ad
const handleShowRewardedAd = () => {
  if (isLoaded) {
    showAd();
  }
};

// Handle reward
useEffect(() => {
  if (reward) {
    console.log('User earned reward:', reward);
    // Give user the reward (coins, hints, etc.)
    clearReward();
  }
}, [reward]);
```

## Testing

### Development Mode
In development (`__DEV__ = true`), the app uses Google's test ad unit IDs:
- Test ads will show during development
- No real revenue is generated
- Safe for testing

### Production Mode
In production builds:
- Replace placeholder ad unit IDs with your actual AdMob ad unit IDs
- Real ads will be served
- Revenue will be generated

## Next Steps

1. **Create Ad Units in AdMob:**
   - Go to AdMob console
   - Create Banner, Interstitial, and Rewarded ad units
   - Copy the ad unit IDs

2. **Update Ad Unit IDs:**
   - Replace `XXXXXXXXXX` in `components/ads/BannerAd.tsx`
   - Replace `XXXXXXXXXX` in `components/ads/InterstitialAd.tsx`
   - Replace `XXXXXXXXXX` in `components/ads/RewardedAd.tsx`

3. **Test Your Implementation:**
   - Build and test on a real Android device
   - Verify ads are loading correctly
   - Test ad interactions

4. **Add More Ad Placements:**
   - Consider adding banner ads to other screens
   - Add rewarded ads for premium features
   - Implement interstitial ads at natural break points

## Troubleshooting

### Common Issues:
1. **Ads not loading:** Check your internet connection and ad unit IDs
2. **Test ads not showing:** Ensure you're in development mode
3. **Production ads not showing:** Verify your ad unit IDs are correct

### Debug Logs:
The ad components include console logs for debugging:
- "Banner ad loaded" / "Banner ad failed to load"
- "Interstitial ad loaded" / "Interstitial ad error"
- "Rewarded ad loaded" / "Rewarded ad error"

## Revenue Optimization Tips

1. **Strategic Placement:** Place ads where they don't interrupt user experience
2. **Frequency Capping:** Don't show ads too frequently (current: every 5 questions)
3. **Ad Formats:** Mix different ad formats for better performance
4. **User Experience:** Always prioritize user experience over ad revenue
