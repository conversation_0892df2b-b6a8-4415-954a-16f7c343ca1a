import { BannerAdComponent } from '@/components/ads';
import { AutoDownloadStatus } from '@/components/AutoDownloadStatus';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useDataManager } from '@/hooks/useDataManager';
import { useThemeColor } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  Dimensions,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity
} from 'react-native';

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const [showDownloadStatus, setShowDownloadStatus] = useState(true);
  const { autoDownloadStatus, bcsSets, loadBCSSets } = useDataManager();

  // Get theme colors
  const primaryColor = useThemeColor({}, 'primary');
  const surfaceColor = useThemeColor({}, 'surface');
  const successColor = useThemeColor({}, 'success');
  const secondaryColor = useThemeColor({}, 'secondary');
  const infoColor = useThemeColor({}, 'info');
   const backgroundColor = useThemeColor({}, 'background');

  const handleQuestionBankPress = () => {
    router.push('/(tabs)/explore');
  };

  const handleSubjectsPress = () => {
    router.push('/(tabs)/bookmarks');
  };

  const handleRandomQuizPress = async () => {
    try {
      // Load BCS sets if not already loaded
      if (bcsSets.length === 0) {
        await loadBCSSets();
      }

      // Check if we have any BCS sets
      if (bcsSets.length === 0) {
        Alert.alert(
          'No Questions Available',
          'Please download some question sets first from the BCS Sets tab.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Pick a random BCS set
      const randomSet = bcsSets[Math.floor(Math.random() * bcsSets.length)];

      // Navigate to questions with random set
      router.push({
        pathname: '/questions',
        params: {
          title: `Random Quiz - ${randomSet.title}`,
          subtitle: randomSet.sub,
          fileUrl: randomSet.file
        }
      });
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to start random quiz. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleProgressPress = () => {
    Alert.alert(
      'My Progress',
      'Progress tracking feature is coming soon! This will show your quiz statistics, completion rates, and performance analytics.',
      [{ text: 'OK' }]
    );
  };

  const handleFavoritesPress = () => {
    router.push('/(tabs)/bookmarks');
  };

  const handleBookmarksPress = () => {
    router.push('/(tabs)/bookmarks');
  };

  const handleContinueLearningPress = () => {
    // Navigate to 41st BCS as shown in the UI
    router.push({
      pathname: '/questions',
      params: {
        title: '41st BCS Preliminary',
        subtitle: 'Continue where you left off',
        fileUrl: 'https://bcs-qb.github.io/questions/41st-bcs-preliminary.json'
      }
    });
  };

  return (
    <ThemedView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={[styles.header, { backgroundColor: primaryColor, shadowColor: primaryColor }]}>
        <ThemedView style={styles.headerContent}>
          <ThemedText type="title" style={[styles.welcomeText, { color: 'white' }]}>
            BCS Question Bank
          </ThemedText>
          <ThemedText type="body" style={[styles.subtitleText, { color: 'white' }]}>
            Master your BCS preparation with comprehensive practice
          </ThemedText>
          <ThemedView style={styles.statsContainer}>
            <ThemedView style={styles.statItem}>
              <ThemedText type="heading" style={[styles.statNumber, { color: 'white' }]}>46+</ThemedText>
              <ThemedText type="caption" style={[styles.statLabel, { color: 'white' }]}>BCS Exams</ThemedText>
            </ThemedView>
            <ThemedView style={styles.statItem}>
              <ThemedText type="heading" style={[styles.statNumber, { color: 'white' }]}>5000+</ThemedText>
              <ThemedText type="caption" style={[styles.statLabel, { color: 'white' }]}>Questions</ThemedText>
            </ThemedView>
            <ThemedView style={styles.statItem}>
              <ThemedText type="heading" style={[styles.statNumber, { color: 'white' }]}>100%</ThemedText>
              <ThemedText type="caption" style={[styles.statLabel, { color: 'white' }]}>Free</ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </ThemedView>

      {/* Auto Download Status */}
      {showDownloadStatus && (
        <AutoDownloadStatus
          status={autoDownloadStatus}
          onDismiss={() => setShowDownloadStatus(false)}
        />
      )}

      <ScrollView style={styles.sectionsContainer} showsVerticalScrollIndicator={false}>
        {/* Main Action Cards */}
        <ThemedView style={styles.mainCardsContainer}>
          <TouchableOpacity
            style={styles.primaryCard}
            onPress={handleQuestionBankPress}
            activeOpacity={0.8}
          >
            <ThemedView style={[styles.card, styles.primaryCardContent, { backgroundColor: primaryColor }]} lightColor={primaryColor} darkColor={primaryColor}>
              <ThemedView style={styles.cardHeader} lightColor="transparent" darkColor="transparent">
                <ThemedView style={styles.primaryCardIcon} lightColor="rgba(255, 255, 255, 0.2)" darkColor="rgba(255, 255, 255, 0.2)">
                  <Text style={styles.primaryIconText}>📚</Text>
                </ThemedView>
                <ThemedView style={styles.cardBadge} lightColor="rgba(255, 255, 255, 0.2)" darkColor="rgba(255, 255, 255, 0.2)">
                  <Text style={styles.badgeText}>46+ Sets</Text>
                </ThemedView>
              </ThemedView>
              <Text style={styles.primaryCardTitle}>BCS Question Sets</Text>
              <Text style={styles.primaryCardDescription}>
                Practice with authentic BCS exam questions organized by year
              </Text>
              <ThemedView style={styles.cardFooter} lightColor="transparent" darkColor="transparent">
                <Text style={styles.footerText}>Start Practicing →</Text>
              </ThemedView>
            </ThemedView>
          </TouchableOpacity>

          {/* <TouchableOpacity
            style={styles.primaryCard}
            activeOpacity={0.8}
            onPress={handleSubjectsPress}
          >
            <ThemedView style={[styles.card, styles.primaryCardContent, { backgroundColor: primaryColor }]} lightColor={primaryColor} darkColor={primaryColor}>
              <ThemedView style={styles.cardHeader} lightColor="transparent" darkColor="transparent">
                <ThemedView style={styles.primaryCardIcon} lightColor="rgba(255, 255, 255, 0.2)" darkColor="rgba(255, 255, 255, 0.2)">
                  <Text style={styles.primaryIconText}>📋</Text>
                </ThemedView>
                <ThemedView style={styles.cardBadge} lightColor="rgba(255, 255, 255, 0.2)" darkColor="rgba(255, 255, 255, 0.2)">
                  <ThemedText style={styles.badgeText}>15+ Subjects</ThemedText>
                </ThemedView>
              </ThemedView>
              <Text style={styles.primaryCardTitle}>Subject Categories</Text>
              <Text style={styles.primaryCardDescription}>
                Study questions organized by specific subject areas
              </Text>
              <ThemedView style={styles.cardFooter} lightColor="transparent" darkColor="transparent">
                <Text style={styles.footerText}>Browse Topics →</Text>
              </ThemedView>
            </ThemedView>
          </TouchableOpacity> */}
        </ThemedView>

        {/* Quick Actions */}
        {/* <ThemedView style={styles.quickActionsContainer}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>Quick Actions</ThemedText>
          <ThemedView style={styles.quickActionsGrid}>
            <TouchableOpacity style={styles.quickActionCard} activeOpacity={0.7} onPress={handleRandomQuizPress}>
              <ThemedView style={[styles.quickActionContent, { backgroundColor: surfaceColor }]}>
                <ThemedView style={[styles.quickActionIcon, { backgroundColor: successColor }]}>
                  <Text style={styles.quickActionIconText}>🎯</Text>
                </ThemedView>
                <ThemedText type="defaultSemiBold" style={styles.quickActionTitle}>Random Quiz</ThemedText>
              </ThemedView>
            </TouchableOpacity>

            <TouchableOpacity style={styles.quickActionCard} activeOpacity={0.7} onPress={handleProgressPress}>
              <ThemedView style={[styles.quickActionContent, { backgroundColor: surfaceColor }]}>
                <ThemedView style={[styles.quickActionIcon, { backgroundColor: infoColor }]}>
                  <Text style={styles.quickActionIconText}>📊</Text>
                </ThemedView>
                <ThemedText type="defaultSemiBold" style={styles.quickActionTitle}>My Progress</ThemedText>
              </ThemedView>
            </TouchableOpacity>

            <TouchableOpacity style={styles.quickActionCard} activeOpacity={0.7} onPress={handleFavoritesPress}>
              <ThemedView style={[styles.quickActionContent, { backgroundColor: surfaceColor }]}>
                <ThemedView style={[styles.quickActionIcon, { backgroundColor: secondaryColor }]}>
                  <Text style={styles.quickActionIconText}>⭐</Text>
                </ThemedView>
                <ThemedText type="defaultSemiBold" style={styles.quickActionTitle}>Favorites</ThemedText>
              </ThemedView>
            </TouchableOpacity>

            <TouchableOpacity style={styles.quickActionCard} activeOpacity={0.7} onPress={handleBookmarksPress}>
              <ThemedView style={[styles.quickActionContent, { backgroundColor: surfaceColor }]}>
                <ThemedView style={[styles.quickActionIcon, { backgroundColor: primaryColor }]}>
                  <Text style={styles.quickActionIconText}>🔖</Text>
                </ThemedView>
                <ThemedText type="defaultSemiBold" style={styles.quickActionTitle}>Bookmarks</ThemedText>
              </ThemedView>
            </TouchableOpacity>
          </ThemedView>
        </ThemedView> */}

        {/* Recent Activity */}
        {/* <ThemedView style={styles.recentActivityContainer}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>Continue Learning</ThemedText>
          <TouchableOpacity
            style={[styles.activityCard, { backgroundColor: surfaceColor }]}
            activeOpacity={0.8}
            onPress={handleContinueLearningPress}
          >
            <ThemedView style={styles.activityContent}>
              <ThemedView style={[styles.activityIcon, { backgroundColor: secondaryColor }]}>
                <Text style={styles.activityIconText}>📖</Text>
              </ThemedView>
              <ThemedView style={styles.activityText}>
                <ThemedText type="defaultSemiBold" style={styles.activityTitle}>
                  41st BCS Preliminary
                </ThemedText>
                <ThemedText type="secondary" style={styles.activityDescription}>
                  Continue where you left off • 15/200 questions
                </ThemedText>
              </ThemedView>
              <ThemedView style={styles.progressContainer}>
                <ThemedView style={styles.progressBar}>
                  <ThemedView style={[styles.progressFill, { width: '7.5%', backgroundColor: primaryColor }]} />
                </ThemedView>
                <ThemedText type="caption" style={styles.progressText}>7.5%</ThemedText>
              </ThemedView>
            </ThemedView>
          </TouchableOpacity>
        </ThemedView> */}
      </ScrollView>

      {/* Banner Ad */}
      <BannerAdComponent style={styles.bannerAd} />

    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 20 : 50,
    paddingBottom: 40,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  headerContent: {
    alignItems: 'center',
    backgroundColor:"transparent",
  },
  welcomeText: {
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  subtitleText: {
    textAlign: 'center',
    marginBottom: 30,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 10,
    backgroundColor:"transparent",
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 16,
    minWidth: 80,
  },
  statNumber: {
    marginBottom: 4,
  },
  statLabel: {
    textAlign: 'center',
  },
  sectionsContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  // Main Cards
  mainCardsContainer: {
    marginTop: 20,
    marginBottom: 32,
  },
  primaryCard: {
    marginBottom: 16,
  },
  card: {
    borderRadius: 20,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
    overflow: 'hidden',
  },
  primaryCardContent: {
    padding: 24,
    minHeight: 160,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  primaryCardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryIconText: {
    fontSize: 24,
  },
  cardBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  primaryCardTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  primaryCardDescription: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  cardFooter: {
    marginTop: 'auto',
  },
  footerText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    opacity: 0.9,
  },
  // Quick Actions
  quickActionsContainer: {
    marginBottom: 32,
  },
  sectionTitle: {
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: (width - 52) / 2,
    marginBottom: 12,
  },
  quickActionContent: {
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  quickActionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickActionIconText: {
    fontSize: 20,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  // Recent Activity
  recentActivityContainer: {
    marginBottom: 32,
  },
  activityCard: {
    borderRadius: 16,
    padding: 20,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  activityContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  activityIconText: {
    fontSize: 24,
  },
  activityText: {
    flex: 1,
    marginRight: 16,
  },
  activityTitle: {
    marginBottom: 4,
  },
  activityDescription: {
    fontSize: 13,
  },
  progressContainer: {
    alignItems: 'flex-end',
  },
  progressBar: {
    width: 60,
    height: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 2,
    marginBottom: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
  },
  bannerAd: {
    marginBottom: 10,
    paddingHorizontal: 20,
  },
});
