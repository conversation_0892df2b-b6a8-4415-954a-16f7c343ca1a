import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';

import { OfflineBanner, useNetworkStatus } from '@/components/OfflineBanner';
import { useColorScheme } from '@/contexts/ThemeContext';

function RootLayoutContent() {
  const colorScheme = useColorScheme();
  const { isOnline } = useNetworkStatus();

  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="questions" options={{ headerShown: false }} />
      </Stack>
      <StatusBar style="auto" />
      <OfflineBanner isVisible={!isOnline} />
      <StatusBar style="light" />
    </ThemeProvider>
  );
}

export default function RootLayout() {
  const { ThemeProvider: CustomThemeProvider } = require('@/contexts/ThemeContext');
  const { useEffect } = require('react');
  const { initializeAdMob } = require('@/utils/adMobInit');

  useEffect(() => {
    initializeAdMob();
  }, []);

  return (
    <CustomThemeProvider>
      <RootLayoutContent />
    </CustomThemeProvider>
  );
}
