{"logs": [{"outputFile": "com.fazlerabbistat.BCSQuestionBank.app-mergeDebugResources-65:/values-kk/values-kk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/5896f7952d78d768b506523789b2ccc8/transformed/play-services-ads-24.3.0/res/values-kk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,250,299,356,425,496,609,674,817,934,1065,1120,1179,1293,1381,1424,1518,1554,1592,1643,1726,1767", "endColumns": "50,48,56,68,70,112,64,142,116,130,54,58,113,87,42,93,35,37,50,82,40,55", "endOffsets": "249,298,355,424,495,608,673,816,933,1064,1119,1178,1292,1380,1423,1517,1553,1591,1642,1725,1766,1822"}, "to": {"startLines": "138,139,140,141,142,143,144,145,146,147,148,149,150,151,153,154,155,156,157,158,159,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12425,12480,12533,12594,12667,12742,12859,12928,13075,13196,13331,13390,13453,13571,13747,13794,13892,13932,13974,14029,14116,14731", "endColumns": "54,52,60,72,74,116,68,146,120,134,58,62,117,91,46,97,39,41,54,86,44,59", "endOffsets": "12475,12528,12589,12662,12737,12854,12923,13070,13191,13326,13385,13448,13566,13658,13789,13887,13927,13969,14024,14111,14156,14786"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,14161", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,14238"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/res/values-kk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4515,4618,4771,4897,5003,5143,5269,5392,5665,5830,5936,6093,6222,6375,6532,6595,6654", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "4613,4766,4892,4998,5138,5264,5387,5496,5825,5931,6088,6217,6370,6527,6590,6649,6727"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/res/values-kk/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5501", "endColumns": "163", "endOffsets": "5660"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,211,294,364,432,507", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "136,206,289,359,427,502,575"}, "to": {"startLines": "48,75,76,78,92,164,165", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4429,7438,7508,7654,8696,14482,14557", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "4510,7503,7586,7719,8759,14552,14625"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "38,39,40,41,42,43,44,166", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3429,3524,3626,3728,3831,3935,4032,14630", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3519,3621,3723,3826,3930,4027,4138,14726"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1034,1098,1193,1263,1326,1433,1498,1565,1626,1693,1755,1809,1923,1982,2043,2097,2172,2298,2386,2472,2573,2663,2753,2895,2967,3040,3177,3266,3347,3404,3460,3526,3597,3674,3745,3825,3897,3973,4054,4124,4224,4311,4383,4474,4567,4641,4716,4808,4860,4942,5008,5092,5178,5240,5304,5367,5436,5540,5644,5738,5838,5899,5959,6043,6127,6203", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "268,346,422,501,595,683,775,887,969,1029,1093,1188,1258,1321,1428,1493,1560,1621,1688,1750,1804,1918,1977,2038,2092,2167,2293,2381,2467,2568,2658,2748,2890,2962,3035,3172,3261,3342,3399,3455,3521,3592,3669,3740,3820,3892,3968,4049,4119,4219,4306,4378,4469,4562,4636,4711,4803,4855,4937,5003,5087,5173,5235,5299,5362,5431,5535,5639,5733,5833,5894,5954,6038,6122,6198,6277"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,69,70,74,77,79,80,81,82,83,84,85,86,87,88,89,90,91,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,152,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3014,3092,3168,3247,3341,4143,4235,4347,6832,6892,6956,7368,7591,7724,7831,7896,7963,8024,8091,8153,8207,8321,8380,8441,8495,8570,8764,8852,8938,9039,9129,9219,9361,9433,9506,9643,9732,9813,9870,9926,9992,10063,10140,10211,10291,10363,10439,10520,10590,10690,10777,10849,10940,11033,11107,11182,11274,11326,11408,11474,11558,11644,11706,11770,11833,11902,12006,12110,12204,12304,12365,13663,14243,14327,14403", "endLines": "5,33,34,35,36,37,45,46,47,68,69,70,74,77,79,80,81,82,83,84,85,86,87,88,89,90,91,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,152,161,162,163", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "318,3087,3163,3242,3336,3424,4230,4342,4424,6887,6951,7046,7433,7649,7826,7891,7958,8019,8086,8148,8202,8316,8375,8436,8490,8565,8691,8847,8933,9034,9124,9214,9356,9428,9501,9638,9727,9808,9865,9921,9987,10058,10135,10206,10286,10358,10434,10515,10585,10685,10772,10844,10935,11028,11102,11177,11269,11321,11403,11469,11553,11639,11701,11765,11828,11897,12001,12105,12199,12299,12360,12420,13742,14322,14398,14477"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/8d4f511b8fe9c142658b655eac750e2f/transformed/browser-1.8.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "67,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6732,7051,7155,7263", "endColumns": "99,103,107,104", "endOffsets": "6827,7150,7258,7363"}}]}]}