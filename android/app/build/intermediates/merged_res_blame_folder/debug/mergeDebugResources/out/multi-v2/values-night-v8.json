{"logs": [{"outputFile": "com.fazlerabbistat.BCSQuestionBank.app-mergeDebugResources-65:/values-night-v8/values-night-v8.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/aa71ddfffa468141c4e43e0245303cf6/transformed/core-splashscreen-1.2.0-alpha02/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "195"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "107", "endLines": "5", "endColumns": "12", "endOffsets": "247"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "6,7,8,9,10,11,12,39", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "252,322,406,490,586,688,790,3772", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "317,401,485,581,683,785,879,3856"}}, {"source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/intermediates/packaged_res/debug/packageDebugResources/values-night-v8/values-night-v8.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "51", "endOffsets": "102"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "884,959,1070,1159,1260,1367,1474,1573,1680,1783,1910,1998,2122,2224,2326,2442,2544,2658,2786,2902,3024,3160,3280,3414,3534,3646,3861,3978,4102,4232,4354,4492,4626,4742", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "954,1065,1154,1255,1362,1469,1568,1675,1778,1905,1993,2117,2219,2321,2437,2539,2653,2781,2897,3019,3155,3275,3409,3529,3641,3767,3973,4097,4227,4349,4487,4621,4737,4857"}}]}]}