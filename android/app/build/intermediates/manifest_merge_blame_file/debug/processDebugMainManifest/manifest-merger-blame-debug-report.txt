1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fazlerabbistat.BCSQuestionBank"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:4:3-75
11-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:2:3-64
12-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:2:20-62
13    <uses-permission
13-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:5:3-63
16-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:5:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:6:3-78
17-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:6:20-76
18
19    <queries>
19-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:7:3-13:13
20        <intent>
20-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:8:5-12:14
21            <action android:name="android.intent.action.VIEW" />
21-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:9:7-58
21-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:9:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:10:7-67
23-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:10:17-65
24
25            <data android:scheme="https" />
25-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:7-37
25-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:13-35
26        </intent>
27        <intent>
27-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-10:18
28            <action android:name="org.chromium.intent.action.PAY" />
28-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-69
28-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-66
29        </intent>
30        <intent>
30-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:9-13:18
31            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
31-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-81
31-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:21-78
32        </intent>
33        <intent>
33-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-16:18
34            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
34-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-88
34-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:21-85
35        </intent> <!-- Query open documents -->
36        <intent>
36-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:15:9-17:18
37            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
37-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:13-79
37-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:21-76
38        </intent>
39        <intent>
39-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:8:9-12:18
40
41            <!-- Required for opening tabs if targeting API 30 -->
42            <action android:name="android.support.customtabs.action.CustomTabsService" />
42-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:13-90
42-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:21-87
43        </intent> <!-- End of CustomTabsService -->
44        <!-- For MRAID capabilities -->
45        <intent>
45-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:52:9-56:18
46            <action android:name="android.intent.action.INSERT" />
46-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:53:13-67
46-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:53:21-64
47
48            <data android:mimeType="vnd.android.cursor.dir/event" />
48-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:7-37
49        </intent>
50        <intent>
50-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:57:9-61:18
51            <action android:name="android.intent.action.VIEW" />
51-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:9:7-58
51-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:9:15-56
52
53            <data android:scheme="sms" />
53-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:7-37
53-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:13-35
54        </intent>
55        <intent>
55-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:62:9-66:18
56            <action android:name="android.intent.action.DIAL" />
56-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:63:13-65
56-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:63:21-62
57
58            <data android:path="tel:" />
58-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:7-37
59        </intent>
60    </queries>
61
62    <uses-permission android:name="android.permission.WAKE_LOCK" />
62-->[:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
62-->[:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-65
63    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
63-->[:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-79
63-->[:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:22-76
64    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
64-->[:react-native-community_netinfo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-76
64-->[:react-native-community_netinfo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-73
65    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
65-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:26:5-79
65-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:26:22-76
66    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
66-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:27:5-82
66-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:27:22-79
67    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
67-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:28:5-88
67-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:28:22-85
68    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
68-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:29:5-83
68-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:29:22-80
69    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
69-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:5-77
69-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:22-74
70
71    <permission
71-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
72        android:name="com.fazlerabbistat.BCSQuestionBank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
72-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
73        android:protectionLevel="signature" />
73-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
74
75    <uses-permission android:name="com.fazlerabbistat.BCSQuestionBank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
75-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
75-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
76
77    <application
77-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:3-33:17
78        android:name="com.fazlerabbistat.BCSQuestionBank.MainApplication"
78-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:16-47
79        android:allowBackup="true"
79-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:162-188
80        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
80-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
81        android:debuggable="true"
82        android:extractNativeLibs="false"
83        android:icon="@mipmap/ic_launcher"
83-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:81-115
84        android:label="@string/app_name"
84-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:48-80
85        android:roundIcon="@mipmap/ic_launcher_round"
85-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:116-161
86        android:supportsRtl="true"
86-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:221-247
87        android:theme="@style/AppTheme"
87-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:189-220
88        android:usesCleartextTraffic="true" >
88-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml:6:18-53
89        <meta-data
89-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:15:5-121
90            android:name="com.google.android.gms.ads.APPLICATION_ID"
90-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:15:16-72
91            android:value="" />
91-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:15:73-89
92        <meta-data
92-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:16:5-135
93            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
93-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:16:16-82
94            android:value="true" />
94-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:16:83-103
95        <meta-data
95-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:17:5-139
96            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
96-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:17:16-86
97            android:value="true" />
97-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:17:87-107
98        <meta-data
98-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:18:5-83
99            android:name="expo.modules.updates.ENABLED"
99-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:18:16-59
100            android:value="false" />
100-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:18:60-81
101        <meta-data
101-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:19:5-105
102            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
102-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:19:16-80
103            android:value="ALWAYS" />
103-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:19:81-103
104        <meta-data
104-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:20:5-99
105            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
105-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:20:16-79
106            android:value="0" />
106-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:20:80-97
107
108        <activity
108-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:5-32:16
109            android:name="com.fazlerabbistat.BCSQuestionBank.MainActivity"
109-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:15-43
110            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
110-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:44-134
111            android:exported="true"
111-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:256-279
112            android:launchMode="singleTask"
112-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:135-166
113            android:screenOrientation="portrait"
113-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:280-316
114            android:theme="@style/Theme.App.SplashScreen"
114-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:210-255
115            android:windowSoftInputMode="adjustResize" >
115-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:167-209
116            <intent-filter>
116-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:22:7-25:23
117                <action android:name="android.intent.action.MAIN" />
117-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:23:9-60
117-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:23:17-58
118
119                <category android:name="android.intent.category.LAUNCHER" />
119-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:24:9-68
119-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:24:19-66
120            </intent-filter>
121            <intent-filter>
121-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:26:7-31:23
122                <action android:name="android.intent.action.VIEW" />
122-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:9:7-58
122-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:9:15-56
123
124                <category android:name="android.intent.category.DEFAULT" />
124-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:28:9-67
124-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:28:19-65
125                <category android:name="android.intent.category.BROWSABLE" />
125-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:10:7-67
125-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:10:17-65
126
127                <data android:scheme="bcsquestionbank" />
127-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:7-37
127-->/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:13-35
128            </intent-filter>
129        </activity>
130
131        <meta-data
131-->[:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:9-17:37
132            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
132-->[:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-81
133            android:value="false" />
133-->[:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-34
134
135        <provider
135-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:9-28:20
136            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
136-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-83
137            android:authorities="com.fazlerabbistat.BCSQuestionBank.fileprovider"
137-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-64
138            android:exported="false"
138-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-37
139            android:grantUriPermissions="true" >
139-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-47
140            <meta-data
140-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-27:63
141                android:name="android.support.FILE_PROVIDER_PATHS"
141-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:17-67
142                android:resource="@xml/file_provider_paths" />
142-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:17-60
143        </provider>
144
145        <meta-data
145-->[:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
146            android:name="org.unimodules.core.AppLoader#react-native-headless"
146-->[:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
147            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
147-->[:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
148        <meta-data
148-->[:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
149            android:name="com.facebook.soloader.enabled"
149-->[:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
150            android:value="true" />
150-->[:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
151
152        <activity
152-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:19:9-21:40
153            android:name="com.facebook.react.devsupport.DevSettingsActivity"
153-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:20:13-77
154            android:exported="false" />
154-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:21:13-37
155
156        <provider
156-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:21:9-30:20
157            android:name="expo.modules.filesystem.FileSystemFileProvider"
157-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:22:13-74
158            android:authorities="com.fazlerabbistat.BCSQuestionBank.FileSystemFileProvider"
158-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:23:13-74
159            android:exported="false"
159-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:24:13-37
160            android:grantUriPermissions="true" >
160-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:25:13-47
161            <meta-data
161-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-27:63
162                android:name="android.support.FILE_PROVIDER_PATHS"
162-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:17-67
163                android:resource="@xml/file_system_provider_paths" />
163-->[:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:17-60
164        </provider>
165
166        <uses-library
166-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
167            android:name="android.ext.adservices"
167-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
168            android:required="false" />
168-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
169
170        <meta-data
170-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:11:9-13:43
171            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
171-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:12:13-84
172            android:value="GlideModule" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
172-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:13:13-40
173        <activity
173-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:73:9-78:43
174            android:name="com.google.android.gms.ads.AdActivity"
174-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:74:13-65
175            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
175-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:75:13-122
176            android:exported="false"
176-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:76:13-37
177            android:theme="@android:style/Theme.Translucent" />
177-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:77:13-61
178
179        <provider
179-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:80:9-85:43
180            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
180-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:81:13-76
181            android:authorities="com.fazlerabbistat.BCSQuestionBank.mobileadsinitprovider"
181-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:82:13-73
182            android:exported="false"
182-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:83:13-37
183            android:initOrder="100" />
183-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:84:13-36
184
185        <service
185-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:87:9-91:43
186            android:name="com.google.android.gms.ads.AdService"
186-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:88:13-64
187            android:enabled="true"
187-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:89:13-35
188            android:exported="false" />
188-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:90:13-37
189
190        <activity
190-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:93:9-97:43
191            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
191-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:94:13-82
192            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
192-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:95:13-122
193            android:exported="false" />
193-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:96:13-37
194        <activity
194-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:98:9-105:43
195            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
195-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:99:13-82
196            android:excludeFromRecents="true"
196-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:100:13-46
197            android:exported="false"
197-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:101:13-37
198            android:launchMode="singleTask"
198-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:102:13-44
199            android:taskAffinity=""
199-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:103:13-36
200            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
200-->[com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:104:13-72
201        <activity
201-->[com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:20:9-22:45
202            android:name="com.google.android.gms.common.api.GoogleApiActivity"
202-->[com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:20:19-85
203            android:exported="false"
203-->[com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:22:19-43
204            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
204-->[com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:21:19-78
205
206        <meta-data
206-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
207            android:name="com.google.android.gms.version"
207-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
208            android:value="@integer/google_play_services_version" />
208-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
209
210        <provider
210-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
211            android:name="androidx.startup.InitializationProvider"
211-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
212            android:authorities="com.fazlerabbistat.BCSQuestionBank.androidx-startup"
212-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
213            android:exported="false" >
213-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
214            <meta-data
214-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
215                android:name="androidx.emoji2.text.EmojiCompatInitializer"
215-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
216                android:value="androidx.startup" />
216-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
217            <meta-data
217-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:36:13-38:52
218                android:name="androidx.work.WorkManagerInitializer"
218-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:37:17-68
219                android:value="androidx.startup" />
219-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:38:17-49
220            <meta-data
220-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
221                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
221-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
222                android:value="androidx.startup" />
222-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
223            <meta-data
223-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
224                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
224-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
225                android:value="androidx.startup" />
225-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
226        </provider>
227
228        <service
228-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:41:9-46:35
229            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
229-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:42:13-88
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:43:13-44
231            android:enabled="@bool/enable_system_alarm_service_default"
231-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:44:13-72
232            android:exported="false" />
232-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:45:13-37
233        <service
233-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:47:9-53:35
234            android:name="androidx.work.impl.background.systemjob.SystemJobService"
234-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:48:13-84
235            android:directBootAware="false"
235-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:49:13-44
236            android:enabled="@bool/enable_system_job_service_default"
236-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:50:13-70
237            android:exported="true"
237-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:51:13-36
238            android:permission="android.permission.BIND_JOB_SERVICE" />
238-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:52:13-69
239        <service
239-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:54:9-59:35
240            android:name="androidx.work.impl.foreground.SystemForegroundService"
240-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:55:13-81
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:56:13-44
242            android:enabled="@bool/enable_system_foreground_service_default"
242-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:57:13-77
243            android:exported="false" />
243-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:58:13-37
244
245        <receiver
245-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:61:9-66:35
246            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
246-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:62:13-88
247            android:directBootAware="false"
247-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:63:13-44
248            android:enabled="true"
248-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:64:13-35
249            android:exported="false" />
249-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:65:13-37
250        <receiver
250-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:67:9-77:20
251            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
251-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:68:13-106
252            android:directBootAware="false"
252-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:69:13-44
253            android:enabled="false"
253-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:70:13-36
254            android:exported="false" >
254-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:71:13-37
255            <intent-filter>
255-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:73:13-76:29
256                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
256-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:17-87
256-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:25-84
257                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
257-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:17-90
257-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:25-87
258            </intent-filter>
259        </receiver>
260        <receiver
260-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:78:9-88:20
261            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
261-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:79:13-104
262            android:directBootAware="false"
262-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:80:13-44
263            android:enabled="false"
263-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:81:13-36
264            android:exported="false" >
264-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:82:13-37
265            <intent-filter>
265-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:84:13-87:29
266                <action android:name="android.intent.action.BATTERY_OKAY" />
266-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:17-77
266-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:25-74
267                <action android:name="android.intent.action.BATTERY_LOW" />
267-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:17-76
267-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:25-73
268            </intent-filter>
269        </receiver>
270        <receiver
270-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:89:9-99:20
271            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
271-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:90:13-104
272            android:directBootAware="false"
272-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:91:13-44
273            android:enabled="false"
273-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:92:13-36
274            android:exported="false" >
274-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:93:13-37
275            <intent-filter>
275-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:95:13-98:29
276                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
276-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:17-83
276-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:25-80
277                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
277-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:17-82
277-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:25-79
278            </intent-filter>
279        </receiver>
280        <receiver
280-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:100:9-109:20
281            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
281-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:101:13-103
282            android:directBootAware="false"
282-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:102:13-44
283            android:enabled="false"
283-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:103:13-36
284            android:exported="false" >
284-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:104:13-37
285            <intent-filter>
285-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:106:13-108:29
286                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
286-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:17-79
286-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:25-76
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:110:9-121:20
290            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
290-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:111:13-88
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:112:13-44
292            android:enabled="false"
292-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:113:13-36
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:114:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:116:13-120:29
295                <action android:name="android.intent.action.BOOT_COMPLETED" />
295-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:17-79
295-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:25-76
296                <action android:name="android.intent.action.TIME_SET" />
296-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:17-73
296-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:25-70
297                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
297-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:17-81
297-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:25-78
298            </intent-filter>
299        </receiver>
300        <receiver
300-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:122:9-131:20
301            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
301-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:123:13-99
302            android:directBootAware="false"
302-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:124:13-44
303            android:enabled="@bool/enable_system_alarm_service_default"
303-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:125:13-72
304            android:exported="false" >
304-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:126:13-37
305            <intent-filter>
305-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:128:13-130:29
306                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
306-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:17-98
306-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:25-95
307            </intent-filter>
308        </receiver>
309        <receiver
309-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:132:9-142:20
310            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
310-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:133:13-78
311            android:directBootAware="false"
311-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:134:13-44
312            android:enabled="true"
312-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:135:13-35
313            android:exported="true"
313-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:136:13-36
314            android:permission="android.permission.DUMP" >
314-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:137:13-57
315            <intent-filter>
315-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:139:13-141:29
316                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
316-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:17-88
316-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:25-85
317            </intent-filter>
318        </receiver>
319        <receiver
319-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
320            android:name="androidx.profileinstaller.ProfileInstallReceiver"
320-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
321            android:directBootAware="false"
321-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
322            android:enabled="true"
322-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
323            android:exported="true"
323-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
324            android:permission="android.permission.DUMP" >
324-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
325            <intent-filter>
325-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
326                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
326-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
326-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
327            </intent-filter>
328            <intent-filter>
328-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
329                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
329-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
329-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
330            </intent-filter>
331            <intent-filter>
331-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
332                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
332-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
332-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
333            </intent-filter>
334            <intent-filter>
334-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
335                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
335-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
335-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
336            </intent-filter>
337        </receiver>
338
339        <service
339-->[androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:25:9-28:40
340            android:name="androidx.room.MultiInstanceInvalidationService"
340-->[androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:26:13-74
341            android:directBootAware="true"
341-->[androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:27:13-43
342            android:exported="false" />
342-->[androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:28:13-37
343    </application>
344
345</manifest>
