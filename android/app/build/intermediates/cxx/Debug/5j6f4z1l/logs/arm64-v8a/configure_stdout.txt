-- The C compiler identification is Clang 18.0.2
-- The CXX compiler identification is Clang 18.0.2
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- The ASM compiler identification is Clang with GNU-like command-line
-- Found assembler: /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang
-- Configuring done
-- Generating done
-- Build files have been written to: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a
