[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/android_gradle_build.json due to:", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/java \\\n  --class-path \\\n  /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar \\\n  com.google.prefab.cli.AppKt \\\n  --build-system \\\n  cmake \\\n  --platform \\\n  android \\\n  --abi \\\n  arm64-v8a \\\n  --os-version \\\n  24 \\\n  --stl \\\n  c++_shared \\\n  --ndk-version \\\n  27 \\\n  --output \\\n  /var/folders/h3/t19rktbd2wj44yb433jshhxc0000gn/T/agp-prefab-staging18066996244806600509/staged-cli-output \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab \\\n  \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/refs/react-native-reanimated/********\" \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/prefab \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab\n", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a'", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a'", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  \"-H/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.******** \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.******** \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a\" \\\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a\" \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  \"-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab\" \\\n  \"-B/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a\" \\\n  -GNinja \\\n  \"-DPROJECT_BUILD_DIR=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build\" \\\n  \"-DPROJECT_ROOT_DIR=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android\" \\\n  \"-DREACT_ANDROID_DIR=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid\" \\\n  -DANDROID_STL=c++_shared \\\n  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\n", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  \"-H/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMA<PERSON>_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.******** \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.******** \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/android.toolchain.cmake \\\n  -DCMA<PERSON>_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a\" \\\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a\" \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  \"-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab\" \\\n  \"-B/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a\" \\\n  -GNinja \\\n  \"-DPROJECT_BUILD_DIR=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build\" \\\n  \"-DPROJECT_ROOT_DIR=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android\" \\\n  \"-DREACT_ANDROID_DIR=/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid\" \\\n  -DANDROID_STL=c++_shared \\\n  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\n", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/compile_commands.json.bin normally", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/compile_commands.json to /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/tools/debug/arm64-v8a/compile_commands.json", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]