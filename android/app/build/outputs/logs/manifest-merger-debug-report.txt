-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:1:1-34:12
MERGED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:1:1-34:12
INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-gesture-handler] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-26:12
MERGED from [:react-native-safe-area-context] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-31:12
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_netinfo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:expo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-mmkv] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:2:1-7:12
MERGED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:2:1-19:12
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] /Users/<USER>/.gradle/caches/8.13/transforms/67d48c5e254af6c4ce9ec61f25cf5235/transformed/expo.modules.splashscreen-0.30.10/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] /Users/<USER>/.gradle/caches/8.13/transforms/64bdd59c9de07fc9b89051e8f03806a8/transformed/expo.modules.systemui-5.0.10/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:2:1-24:12
MERGED from [:expo-constants] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/1439498032ce0c8b52a3fc0d2a0054ca/transformed/expo.modules.blur-14.1.5/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:2:1-33:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecae0e31dbc5f8f717fc2c392dddb4ae/transformed/expo.modules.lineargradient-14.1.5/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/478a7daaf5df250b58ae7be0b2bd8ba4/transformed/expo.modules.linking-7.1.7/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f73d2501712635ca4c70639fcec74b73/transformed/expo.modules.network-7.1.5/AndroidManifest.xml:2:1-10:12
MERGED from [host.exp.exponent:expo.modules.sqlite:15.2.14] /Users/<USER>/.gradle/caches/8.13/transforms/0d056d7efc996171a2e4d0407a88af5c/transformed/expo.modules.sqlite-15.2.14/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-ads:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5896f7952d78d768b506523789b2ccc8/transformed/play-services-ads-24.3.0/AndroidManifest.xml:17:1-25:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/0bde2a7ee8a4fedb82d377f8a9949666/transformed/ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.13/transforms/aa71ddfffa468141c4e43e0245303cf6/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/7ceefd5bc7d6e30a327f1509056f856d/transformed/glide-plugin-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/c02f7be09f8461e07996d25cc4d306d9/transformed/awebp-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/51930be23003f579f3d420f5dae068a5/transformed/apng-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/dd9562f437d7a954611986333f652e5f/transformed/gif-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/5866d1f795aea78dbd8ccf03b464fd9b/transformed/avif-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/6e87983048c5d0adb4e5bf192cad8685/transformed/frameanimation-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/5aa1ec42bdb188598da4a46342470b57/transformed/avif-integration-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/72d39494f24b2cfae9568848e697a111/transformed/glide-transformations-4.3.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:2:1-16:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/bbeeba403c008876f9fc01e5e82c994b/transformed/glide-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:17:1-115:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94221fe670c842270b30783e4aad6c3f/transformed/user-messaging-platform-3.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8375261273fa6e947a985039ae8d607b/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/f468125c87f34176aea91eec4c999d16/transformed/play-services-appset-16.0.1/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/319cc1a8ce867b15e5788cce762f00b1/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce37a28011a1b60b4e3d132dbd8b1975/transformed/activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0b3cd90608d731515ee5b0c8986ce7d6/transformed/activity-ktx-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.14.0] /Users/<USER>/.gradle/caches/8.13/transforms/04986074170a253cff14fce64be9a483/transformed/webkit-1.14.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/4bae3fed21d5f02d6bf43c03532e29f6/transformed/core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/8d4f511b8fe9c142658b655eac750e2f/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/042c063257c458b0d7190d8859d3193e/transformed/lifecycle-service-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/9478ca0554caeea9ae571964e0cb2ca6/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/67803a5cacc8f200daadaf81b44424d4/transformed/gifdecoder-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.13/transforms/5e715ff8949f07ccecbd27a8f3ccfe53/transformed/exifinterface-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/f63ce227f7d17bf7ab57fdbbe21e7d6e/transformed/sqlite-framework-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2205f6b3486e07f861fa8fcf19d49b14/transformed/sqlite-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:2:1-17:12
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.13/transforms/ac9879374d2e96acc4c0b73efbd01c6c/transformed/androidsvg-aar-1.4/AndroidManifest.xml:2:1-11:12
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:2:3-64
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:7:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:8:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:23:5-67
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:2:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:3:3-77
MERGED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:15:5-17:38
MERGED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:15:5-17:38
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:10:5-80
	android:maxSdkVersion
		ADDED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:17:9-35
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:3:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:4:3-75
MERGED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:4:3-75
MERGED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:4:3-75
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:16:5-78
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:4:20-73
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:5:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:7:5-66
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:5:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:6:3-78
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:9:5-81
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:6:20-76
queries
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:7:3-13:13
MERGED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-17:15
MERGED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-17:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:7:5-13:15
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:8:5-12:14
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:9:7-58
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:9:15-56
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:10:7-67
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:10:17-65
data
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:7-37
	android:scheme
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:11:13-35
application
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:3-33:17
MERGED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:3-33:17
MERGED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:3-33:17
INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml:6:5-162
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-24:19
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-24:19
MERGED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:5-29:19
MERGED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:5-29:19
MERGED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:18:5-22:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:20:5-31:19
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:10:5-14:19
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8375261273fa6e947a985039ae8d607b/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8375261273fa6e947a985039ae8d607b/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/f468125c87f34176aea91eec4c999d16/transformed/play-services-appset-16.0.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/f468125c87f34176aea91eec4c999d16/transformed/play-services-appset-16.0.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/319cc1a8ce867b15e5788cce762f00b1/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/319cc1a8ce867b15e5788cce762f00b1/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:24:5-29:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:5:5-6:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:221-247
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:221-247
	android:label
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:48-80
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:48-80
	tools:ignore
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:116-161
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:116-161
	tools:targetApi
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml:6:54-74
	android:icon
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:81-115
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:81-115
	android:allowBackup
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:162-188
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:162-188
	android:theme
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:189-220
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:189-220
	tools:replace
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml:6:18-53
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:16-47
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:14:16-47
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:15:5-159
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-14:32
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-14:32
	tools:replace
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:15:128-157
	android:value
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:15:73-127
		REJECTED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-29
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:15:16-72
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:16:5-135
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:9-23:36
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:9-23:36
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:107:9-109:36
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:107:9-109:36
	tools:replace
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:16:104-133
	android:value
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:16:83-103
		REJECTED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-33
		REJECTED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:109:13-33
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:16:16-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:17:5-139
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-20:36
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-20:36
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:110:9-112:36
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:110:9-112:36
	tools:replace
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:17:108-137
	android:value
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:17:87-107
		REJECTED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-33
		REJECTED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:112:13-33
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:17:16-86
meta-data#expo.modules.updates.ENABLED
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:18:5-83
	android:value
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:18:60-81
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:18:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:19:5-105
	android:value
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:19:81-103
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:19:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:20:5-99
	android:value
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:20:80-97
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:20:16-79
activity#com.fazlerabbistat.BCSQuestionBank.MainActivity
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:5-32:16
	android:screenOrientation
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:280-316
	android:launchMode
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:135-166
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:167-209
	android:exported
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:256-279
	android:configChanges
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:44-134
	android:theme
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:210-255
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:21:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:22:7-25:23
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:23:9-60
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:23:17-58
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:24:9-68
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:24:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:bcsquestionbank
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:26:7-31:23
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:28:9-67
	android:name
		ADDED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/main/AndroidManifest.xml:28:19-65
uses-sdk
INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml
MERGED from [:react-native-gesture-handler] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-mmkv] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-mmkv] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:5:5-44
MERGED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:4:5-44
MERGED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:4:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] /Users/<USER>/.gradle/caches/8.13/transforms/67d48c5e254af6c4ce9ec61f25cf5235/transformed/expo.modules.splashscreen-0.30.10/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] /Users/<USER>/.gradle/caches/8.13/transforms/67d48c5e254af6c4ce9ec61f25cf5235/transformed/expo.modules.splashscreen-0.30.10/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] /Users/<USER>/.gradle/caches/8.13/transforms/64bdd59c9de07fc9b89051e8f03806a8/transformed/expo.modules.systemui-5.0.10/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] /Users/<USER>/.gradle/caches/8.13/transforms/64bdd59c9de07fc9b89051e8f03806a8/transformed/expo.modules.systemui-5.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/1439498032ce0c8b52a3fc0d2a0054ca/transformed/expo.modules.blur-14.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/1439498032ce0c8b52a3fc0d2a0054ca/transformed/expo.modules.blur-14.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecae0e31dbc5f8f717fc2c392dddb4ae/transformed/expo.modules.lineargradient-14.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecae0e31dbc5f8f717fc2c392dddb4ae/transformed/expo.modules.lineargradient-14.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/478a7daaf5df250b58ae7be0b2bd8ba4/transformed/expo.modules.linking-7.1.7/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/478a7daaf5df250b58ae7be0b2bd8ba4/transformed/expo.modules.linking-7.1.7/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f73d2501712635ca4c70639fcec74b73/transformed/expo.modules.network-7.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f73d2501712635ca4c70639fcec74b73/transformed/expo.modules.network-7.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.sqlite:15.2.14] /Users/<USER>/.gradle/caches/8.13/transforms/0d056d7efc996171a2e4d0407a88af5c/transformed/expo.modules.sqlite-15.2.14/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.sqlite:15.2.14] /Users/<USER>/.gradle/caches/8.13/transforms/0d056d7efc996171a2e4d0407a88af5c/transformed/expo.modules.sqlite-15.2.14/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5896f7952d78d768b506523789b2ccc8/transformed/play-services-ads-24.3.0/AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5896f7952d78d768b506523789b2ccc8/transformed/play-services-ads-24.3.0/AndroidManifest.xml:21:5-23:52
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/0bde2a7ee8a4fedb82d377f8a9949666/transformed/ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/0bde2a7ee8a4fedb82d377f8a9949666/transformed/ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.13/transforms/aa71ddfffa468141c4e43e0245303cf6/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.13/transforms/aa71ddfffa468141c4e43e0245303cf6/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/7ceefd5bc7d6e30a327f1509056f856d/transformed/glide-plugin-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/7ceefd5bc7d6e30a327f1509056f856d/transformed/glide-plugin-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/c02f7be09f8461e07996d25cc4d306d9/transformed/awebp-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/c02f7be09f8461e07996d25cc4d306d9/transformed/awebp-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/51930be23003f579f3d420f5dae068a5/transformed/apng-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/51930be23003f579f3d420f5dae068a5/transformed/apng-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/dd9562f437d7a954611986333f652e5f/transformed/gif-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/dd9562f437d7a954611986333f652e5f/transformed/gif-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/5866d1f795aea78dbd8ccf03b464fd9b/transformed/avif-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/5866d1f795aea78dbd8ccf03b464fd9b/transformed/avif-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/6e87983048c5d0adb4e5bf192cad8685/transformed/frameanimation-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/6e87983048c5d0adb4e5bf192cad8685/transformed/frameanimation-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/5aa1ec42bdb188598da4a46342470b57/transformed/avif-integration-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/5aa1ec42bdb188598da4a46342470b57/transformed/avif-integration-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/72d39494f24b2cfae9568848e697a111/transformed/glide-transformations-4.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/72d39494f24b2cfae9568848e697a111/transformed/glide-transformations-4.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/bbeeba403c008876f9fc01e5e82c994b/transformed/glide-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/bbeeba403c008876f9fc01e5e82c994b/transformed/glide-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94221fe670c842270b30783e4aad6c3f/transformed/user-messaging-platform-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94221fe670c842270b30783e4aad6c3f/transformed/user-messaging-platform-3.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8375261273fa6e947a985039ae8d607b/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8375261273fa6e947a985039ae8d607b/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/f468125c87f34176aea91eec4c999d16/transformed/play-services-appset-16.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/f468125c87f34176aea91eec4c999d16/transformed/play-services-appset-16.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/319cc1a8ce867b15e5788cce762f00b1/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/319cc1a8ce867b15e5788cce762f00b1/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce37a28011a1b60b4e3d132dbd8b1975/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce37a28011a1b60b4e3d132dbd8b1975/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0b3cd90608d731515ee5b0c8986ce7d6/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0b3cd90608d731515ee5b0c8986ce7d6/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.14.0] /Users/<USER>/.gradle/caches/8.13/transforms/04986074170a253cff14fce64be9a483/transformed/webkit-1.14.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] /Users/<USER>/.gradle/caches/8.13/transforms/04986074170a253cff14fce64be9a483/transformed/webkit-1.14.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/4bae3fed21d5f02d6bf43c03532e29f6/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/4bae3fed21d5f02d6bf43c03532e29f6/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/8d4f511b8fe9c142658b655eac750e2f/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/8d4f511b8fe9c142658b655eac750e2f/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/042c063257c458b0d7190d8859d3193e/transformed/lifecycle-service-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/042c063257c458b0d7190d8859d3193e/transformed/lifecycle-service-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/9478ca0554caeea9ae571964e0cb2ca6/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/9478ca0554caeea9ae571964e0cb2ca6/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/67803a5cacc8f200daadaf81b44424d4/transformed/gifdecoder-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/67803a5cacc8f200daadaf81b44424d4/transformed/gifdecoder-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.13/transforms/5e715ff8949f07ccecbd27a8f3ccfe53/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.13/transforms/5e715ff8949f07ccecbd27a8f3ccfe53/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/f63ce227f7d17bf7ab57fdbbe21e7d6e/transformed/sqlite-framework-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/f63ce227f7d17bf7ab57fdbbe21e7d6e/transformed/sqlite-framework-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2205f6b3486e07f861fa8fcf19d49b14/transformed/sqlite-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2205f6b3486e07f861fa8fcf19d49b14/transformed/sqlite-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.13/transforms/ac9879374d2e96acc4c0b73efbd01c6c/transformed/androidsvg-aar-1.4/AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.13/transforms/ac9879374d2e96acc4c0b73efbd01c6c/transformed/androidsvg-aar-1.4/AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:4:5-44
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5896f7952d78d768b506523789b2ccc8/transformed/play-services-ads-24.3.0/AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/src/debug/AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:25:5-68
	android:name
		ADDED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-79
MERGED from [:react-native-community_netinfo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [:react-native-community_netinfo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:12:5-79
MERGED from [BareExpo:expo.modules.image:2.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/596613bba2a9b21a96dd635d93241e61/transformed/expo.modules.image-2.4.0/AndroidManifest.xml:12:5-79
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f73d2501712635ca4c70639fcec74b73/transformed/expo.modules.network-7.1.5/AndroidManifest.xml:8:5-79
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f73d2501712635ca4c70639fcec74b73/transformed/expo.modules.network-7.1.5/AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:22-76
meta-data#com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT
ADDED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:9-17:37
	android:value
		ADDED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-34
	android:name
		ADDED from [:react-native-google-mobile-ads] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-81
intent#action:name:org.chromium.intent.action.PAY
ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-10:18
action#org.chromium.intent.action.PAY
ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-69
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-66
intent#action:name:org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:9-13:18
action#org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-81
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:21-78
intent#action:name:org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-16:18
action#org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-88
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:21-85
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:9-28:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-47
	android:authorities
		ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-64
	android:exported
		ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-37
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-27:63
	android:resource
		ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:17-60
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:17-67
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:react-native-community_netinfo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-76
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f73d2501712635ca4c70639fcec74b73/transformed/expo.modules.network-7.1.5/AndroidManifest.xml:7:5-76
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f73d2501712635ca4c70639fcec74b73/transformed/expo.modules.network-7.1.5/AndroidManifest.xml:7:5-76
	android:name
		ADDED from [:react-native-community_netinfo] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-73
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:20:13-77
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:13-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:26:13-48
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:22:13-74
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:13-90
	android:name
		ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:21-87
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.13/transforms/800872c1b4a0f5badd69d88d3e188c4a/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:12:13-84
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8375261273fa6e947a985039ae8d607b/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8375261273fa6e947a985039ae8d607b/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.13/transforms/bfb110a38e1459f851cda0a659e9fbb9/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ed6e9f45f7eac9bf1aa3d5f0004a1e3b/transformed/play-services-ads-api-24.3.0/AndroidManifest.xml:99:13-82
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9c306295f50d6d74d87997feb0c3e012/transformed/play-services-base-18.0.0/AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba3900a91988fc0d8d00d1e2bc81a3aa/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:25-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.fazlerabbistat.BCSQuestionBank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.fazlerabbistat.BCSQuestionBank.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.13/transforms/74f5d2ebdb8ab6169bd42a59e3a707c0/transformed/room-runtime-2.2.5/AndroidManifest.xml:26:13-74
