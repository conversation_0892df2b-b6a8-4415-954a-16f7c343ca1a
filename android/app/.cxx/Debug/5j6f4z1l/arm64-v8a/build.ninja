# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_core cmake_object_order_depends_target_react-native-mmkv cmake_object_order_depends_target_react_codegen_RNCWebViewSpec cmake_object_order_depends_target_react_codegen_RNEdgeToEdge cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec cmake_object_order_depends_target_react_codegen_RNMmkvSpec cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles/appmodules.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles/appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles/appmodules.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni
  TARGET_COMPILE_PDB = CMakeFiles/appmodules.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libappmodules.pdb"

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles/appmodules.dir/OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles/appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles/appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles/appmodules.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libappmodules.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libappmodules.so

build /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/RNGoogleMobileAdsSpecJSI-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o CMakeFiles/appmodules.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.so /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.so /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.so RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/libz.so /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.so /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.so /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.so RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge RNGoogleMobileAdsSpec_autolinked_build/react_codegen_RNGoogleMobileAdsSpec RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.so"  "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.so"  "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.so"  RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a  /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/libz.so  /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so  -landroid  /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles/appmodules.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles/appmodules.dir/
  TARGET_FILE = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libappmodules.so"
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libappmodules.pdb"


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rnasyncstorage_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rnasyncstorage_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rngesturehandler_codegen_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rngesturehandler_codegen_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNGoogleMobileAdsSpec


#############################################
# Order-only phony target for react_codegen_RNGoogleMobileAdsSpec

build cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec: phony || RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/RNGoogleMobileAdsSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir
  TARGET_COMPILE_PDB = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/
  TARGET_PDB = ""

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec
  TARGET_COMPILE_PDB = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/
  TARGET_PDB = ""

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec
  TARGET_COMPILE_PDB = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/
  TARGET_PDB = ""

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec
  TARGET_COMPILE_PDB = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/
  TARGET_PDB = ""

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/RNGoogleMobileAdsSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/RNGoogleMobileAdsSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/RNGoogleMobileAdsSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec
  TARGET_COMPILE_PDB = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/
  TARGET_PDB = ""

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec
  TARGET_COMPILE_PDB = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/
  TARGET_PDB = ""

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec
  TARGET_COMPILE_PDB = RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNGoogleMobileAdsSpec

build RNGoogleMobileAdsSpec_autolinked_build/react_codegen_RNGoogleMobileAdsSpec: phony RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/RNGoogleMobileAdsSpecJSI-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNGoogleMobileAdsSpec_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNGoogleMobileAdsSpec_autolinked_build/edit_cache: phony RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNGoogleMobileAdsSpec_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNGoogleMobileAdsSpec_autolinked_build/rebuild_cache: phony RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNMmkvSpec


#############################################
# Order-only phony target for react_codegen_RNMmkvSpec

build cmake_object_order_depends_target_react_codegen_RNMmkvSpec: phony || RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/RNMmkvSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNMmkvSpec

build RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec: phony RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNMmkvSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNMmkvSpec_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNMmkvSpec_autolinked_build/edit_cache: phony RNMmkvSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNMmkvSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNMmkvSpec_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNMmkvSpec_autolinked_build/rebuild_cache: phony RNMmkvSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react-native-mmkv


#############################################
# Order-only phony target for react-native-mmkv

build cmake_object_order_depends_target_react-native-mmkv: phony || cmake_object_order_depends_target_core cmake_object_order_depends_target_react_codegen_RNMmkvSpec

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/src/main/cpp/AndroidLogger.cpp.o: CXX_COMPILER__react-native-mmkv_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/src/main/cpp/AndroidLogger.cpp || cmake_object_order_depends_target_react-native-mmkv
  DEFINES = -Dreact_native_mmkv_EXPORTS
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/src/main/cpp/AndroidLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/src/main/cpp
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.pdb"

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp.o: CXX_COMPILER__react-native-mmkv_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp || cmake_object_order_depends_target_react-native-mmkv
  DEFINES = -Dreact_native_mmkv_EXPORTS
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.pdb"

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp.o: CXX_COMPILER__react-native-mmkv_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp || cmake_object_order_depends_target_react-native-mmkv
  DEFINES = -Dreact_native_mmkv_EXPORTS
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target react-native-mmkv


#############################################
# Link the shared library /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.so

build /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.so: CXX_SHARED_LIBRARY_LINKER__react-native-mmkv_Debug RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/src/main/cpp/AndroidLogger.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp.o | RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/libz.so /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a  /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so  -landroid  /Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/24/libz.so  /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreact-native-mmkv.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/
  TARGET_FILE = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.so"
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.pdb"


#############################################
# Utility command for edit_cache

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNMmkvSpec_cxxmodule_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNMmkvSpec_cxxmodule_autolinked_build/edit_cache: phony RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNMmkvSpec_cxxmodule_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNMmkvSpec_cxxmodule_autolinked_build/rebuild_cache: phony RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target core


#############################################
# Order-only phony target for core

build cmake_object_order_depends_target_core: phony || RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_Android.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV_Android.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_Android.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_IO.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV_IO.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_IO.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_OSX.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKVLog.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog_Android.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKVLog_Android.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog_Android.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputData.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData_OSX.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputData_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputDataCrypt.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt_OSX.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputDataCrypt_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedOutputData.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedOutputData.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedOutputData.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/KeyValueHolder.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/KeyValueHolder.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/KeyValueHolder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/PBUtility.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/PBUtility.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/PBUtility.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MiniPBCoder.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder_OSX.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MiniPBCoder_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMBuffer.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMBuffer.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMBuffer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Win32.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock_Win32.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Win32.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Android.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock_Android.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Android.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Android.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Android.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Android.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Linux.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Linux.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Linux.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Win32.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Win32.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Win32.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_OSX.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/ThreadLock.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock_Win32.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/ThreadLock_Win32.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock_Win32.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/AESCrypt.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/AESCrypt.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/AESCrypt.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes_core.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aes_core.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes_core.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_cfb128.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_cfb128.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_cfb128.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_dgst.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_md5_dgst.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_dgst.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_one.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_md5_one.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_one.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/crc32_armv8.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/crc32/crc32_armv8.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/crc32_armv8.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/zlib/crc32.cpp.o: CXX_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/crc32/zlib/crc32.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/zlib/crc32.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/zlib
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aesv8-armx.S.o: ASM_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aesv8-armx.S || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aesv8-armx.S.o.d
  FLAGS = -x assembler-with-cpp -march=armv8-a+crypto -fno-limit-debug-info  -fPIC
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes-armv4.S.o: ASM_COMPILER__core_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aes-armv4.S || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes-armv4.S.o.d
  FLAGS = -x assembler-with-cpp -march=armv8-a+crypto -fno-limit-debug-info  -fPIC
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core"
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target core


#############################################
# Link the static library RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a

build RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a: CXX_STATIC_LIBRARY_LINKER__core_Debug RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_Android.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_IO.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog_Android.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedOutputData.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/KeyValueHolder.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/PBUtility.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMBuffer.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Win32.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Android.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Android.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Linux.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Win32.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock_Win32.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/AESCrypt.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes_core.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_cfb128.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_dgst.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_one.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/crc32_armv8.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/zlib/crc32.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aesv8-armx.S.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes-armv4.S.o
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/core.pdb
  TARGET_FILE = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.pdb


#############################################
# Utility command for edit_cache

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNMmkvSpec_cxxmodule_autolinked_build/core" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNMmkvSpec_cxxmodule_autolinked_build/core/edit_cache: phony RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNMmkvSpec_cxxmodule_autolinked_build/core" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNMmkvSpec_cxxmodule_autolinked_build/core/rebuild_cache: phony RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rnreanimated_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rnreanimated_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.so

build /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o | /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_FILE = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.so"
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.pdb"


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/safeareacontext_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/safeareacontext_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.so

build /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_FILE = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.so"
  TARGET_PDB = "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.pdb"


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rnscreens_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rnscreens_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNVectorIconsSpec


#############################################
# Order-only phony target for react_codegen_RNVectorIconsSpec

build cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec: phony || RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNVectorIconsSpec_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNVectorIconsSpec_autolinked_build/edit_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNVectorIconsSpec_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNVectorIconsSpec_autolinked_build/rebuild_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNCWebViewSpec


#############################################
# Order-only phony target for react_codegen_RNCWebViewSpec

build cmake_object_order_depends_target_react_codegen_RNCWebViewSpec: phony || RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNCWebViewSpec_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNCWebViewSpec_autolinked_build/edit_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNCWebViewSpec_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCWebViewSpec_autolinked_build/rebuild_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNEdgeToEdge


#############################################
# Order-only phony target for react_codegen_RNEdgeToEdge

build cmake_object_order_depends_target_react_codegen_RNEdgeToEdge: phony || RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o


#############################################
# Utility command for edit_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNEdgeToEdge_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNEdgeToEdge_autolinked_build/edit_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNEdgeToEdge_autolinked_build" && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -B"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNEdgeToEdge_autolinked_build/rebuild_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libappmodules.so

build core: phony RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a

build libappmodules.so: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libappmodules.so

build libcore.a: phony RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a

build libreact-native-mmkv.so: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.so

build libreact_codegen_rnscreens.so: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.so

build libreact_codegen_safeareacontext.so: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.so

build react-native-mmkv: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.so

build react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

build react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

build react_codegen_RNGoogleMobileAdsSpec: phony RNGoogleMobileAdsSpec_autolinked_build/react_codegen_RNGoogleMobileAdsSpec

build react_codegen_RNMmkvSpec: phony RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec

build react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.so

build react_codegen_safeareacontext: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a

build all: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libappmodules.so rnasyncstorage_autolinked_build/all rngesturehandler_codegen_autolinked_build/all RNGoogleMobileAdsSpec_autolinked_build/all RNMmkvSpec_autolinked_build/all RNMmkvSpec_cxxmodule_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all RNVectorIconsSpec_autolinked_build/all RNCWebViewSpec_autolinked_build/all RNEdgeToEdge_autolinked_build/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNCWebViewSpec_autolinked_build

build RNCWebViewSpec_autolinked_build/all: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNEdgeToEdge_autolinked_build

build RNEdgeToEdge_autolinked_build/all: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNGoogleMobileAdsSpec_autolinked_build

build RNGoogleMobileAdsSpec_autolinked_build/all: phony RNGoogleMobileAdsSpec_autolinked_build/react_codegen_RNGoogleMobileAdsSpec

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNMmkvSpec_autolinked_build

build RNMmkvSpec_autolinked_build/all: phony RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNMmkvSpec_cxxmodule_autolinked_build

build RNMmkvSpec_cxxmodule_autolinked_build/all: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact-native-mmkv.so RNMmkvSpec_cxxmodule_autolinked_build/core/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNMmkvSpec_cxxmodule_autolinked_build/core

build RNMmkvSpec_cxxmodule_autolinked_build/core/all: phony RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/RNVectorIconsSpec_autolinked_build

build RNVectorIconsSpec_autolinked_build/all: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: /Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/intermediates/cxx/Debug/5j6f4z1l/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/CMakeFiles/cmake.verify_globs | /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/CMakeFiles/VerifyGlobs.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeASMCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeASMInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineASMCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestASMCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-ASM.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-ASM.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeASMCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a/CMakeFiles/VerifyGlobs.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt /Users/<USER>/Development/expo/BCS$ Question$ Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeASMCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeASMInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineASMCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestASMCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-ASM.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-ASM.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/hooks/pre/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.********/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeASMCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
