[{"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles/appmodules.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/build/generated/autolinking/src/main/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles/appmodules.dir/OnLoad.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/RNGoogleMobileAdsSpec-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/RNGoogleMobileAdsSpec-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/RNGoogleMobileAdsSpecJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/RNGoogleMobileAdsSpecJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/RNGoogleMobileAdsSpecJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/RNMmkvSpec-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/RNMmkvSpec-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/States.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_native_mmkv_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/src/main/cpp/AndroidLogger.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/src/main/cpp/AndroidLogger.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/src/main/cpp/AndroidLogger.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_native_mmkv_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_native_mmkv_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/../cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_Android.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV_Android.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV_Android.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_IO.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV_IO.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV_IO.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_OSX.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV_OSX.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKV_OSX.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKVLog.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKVLog.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog_Android.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKVLog_Android.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMKVLog_Android.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputData.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputData.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData_OSX.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputData_OSX.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputData_OSX.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputDataCrypt.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputDataCrypt.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt_OSX.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputDataCrypt_OSX.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedInputDataCrypt_OSX.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedOutputData.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedOutputData.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/CodedOutputData.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/KeyValueHolder.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/KeyValueHolder.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/KeyValueHolder.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/PBUtility.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/PBUtility.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/PBUtility.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MiniPBCoder.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MiniPBCoder.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder_OSX.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MiniPBCoder_OSX.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MiniPBCoder_OSX.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMBuffer.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMBuffer.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MMBuffer.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Win32.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock_Win32.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock_Win32.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Android.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock_Android.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock_Android.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Android.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Android.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Android.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Linux.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Linux.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Linux.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Win32.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Win32.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Win32.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_OSX.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_OSX.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_OSX.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/ThreadLock.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/ThreadLock.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock_Win32.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/ThreadLock_Win32.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/ThreadLock_Win32.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/AESCrypt.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/AESCrypt.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/AESCrypt.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes_core.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aes_core.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aes_core.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_cfb128.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_cfb128.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_cfb128.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_dgst.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_md5_dgst.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_md5_dgst.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_one.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_md5_one.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_md5_one.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/crc32_armv8.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/crc32/crc32_armv8.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/crc32/crc32_armv8.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/zlib/crc32.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/crc32/zlib/crc32.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/crc32/zlib/crc32.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang -target aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\"  -x assembler-with-cpp -march=armv8-a+crypto -fno-limit-debug-info  -fPIC -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aesv8-armx.S.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aesv8-armx.S\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aesv8-armx.S"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang -target aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core\"  -x assembler-with-cpp -march=armv8-a+crypto -fno-limit-debug-info  -fPIC -o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes-armv4.S.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aes-armv4.S\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aes-armv4.S"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Development/expo/BCS_Question_Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.********/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/.\" -I\"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge\" -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o -c \"/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp\"", "file": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp"}]