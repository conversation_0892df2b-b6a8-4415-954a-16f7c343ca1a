{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12], "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-0106fe44c872d3a937be.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [8]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-c2760451794aeae0b5be.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [9]}, {"build": "RNGoogleMobileAdsSpec_autolinked_build", "jsonFile": "directory-RNGoogleMobileAdsSpec_autolinked_build-Debug-fb60819434642b5f6f28.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "RNMmkvSpec_autolinked_build", "jsonFile": "directory-RNMmkvSpec_autolinked_build-Debug-50050cc109e56c77068e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "RNMmkvSpec_cxxmodule_autolinked_build", "childIndexes": [6], "jsonFile": "directory-RNMmkvSpec_cxxmodule_autolinked_build-Debug-175ae3cc5a272eefffd3.json", "minimumCMakeVersion": {"string": "3.9.0"}, "parentIndex": 0, "projectIndex": 1, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android", "targetIndexes": [2]}, {"build": "RNMmkvSpec_cxxmodule_autolinked_build/core", "jsonFile": "directory-RNMmkvSpec_cxxmodule_autolinked_build.core-Debug-67dd174b16fae0001ed0.json", "minimumCMakeVersion": {"string": "3.10.0"}, "parentIndex": 5, "projectIndex": 2, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/MMKV/Core", "targetIndexes": [1]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-a1553c64f5a8c0ac7b78.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [10]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-967af976f096b13f14ba.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [12]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-b38b8548d0a12bb82925.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [11]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-eb129410a787a815f5ad.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-c567b32d5d5ffe4313af.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "RNEdgeToEdge_autolinked_build", "jsonFile": "directory-RNEdgeToEdge_autolinked_build-Debug-94fc193439b1f427d91c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni", "targetIndexes": [4]}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0, 1, 2, 3, 4, 7, 8, 9, 10, 11, 12], "name": "appmodules", "targetIndexes": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}, {"childIndexes": [2], "directoryIndexes": [5], "name": "ReactNativeMmkv", "parentIndex": 0, "targetIndexes": [2]}, {"directoryIndexes": [6], "name": "core", "parentIndex": 1, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-b3c5342753c57eede544.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 6, "id": "core::@1b9a7d546b295b7d0867", "jsonFile": "target-core-Debug-d7b8c7ef93840fc6d011.json", "name": "core", "projectIndex": 2}, {"directoryIndex": 5, "id": "react-native-mmkv::@4ae6a1e65d3e68ba0197", "jsonFile": "target-react-native-mmkv-Debug-1550cda87021055a523d.json", "name": "react-native-mmkv", "projectIndex": 1}, {"directoryIndex": 11, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-8c7b776656e5acda09d5.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 12, "id": "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e", "jsonFile": "target-react_codegen_RNEdgeToEdge-Debug-5cd510b80ef6413372cc.json", "name": "react_codegen_RNEdgeToEdge", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c", "jsonFile": "target-react_codegen_RNGoogleMobileAdsSpec-Debug-fa8b32d54cca80bcca66.json", "name": "react_codegen_RNGoogleMobileAdsSpec", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_RNMmkvSpec::@7541eabbae598da31a69", "jsonFile": "target-react_codegen_RNMmkvSpec-Debug-1bd01882a9cb6eb9cd04.json", "name": "react_codegen_RNMmkvSpec", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-500862ae0d8d15767877.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-6b7e8d3df2254cc64949.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-792bdd1b36471e22914e.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-d771d0fbfe131bcd6cef.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-1a300a4b604aa8a242a0.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-d57f1090a99075b487b5.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android/app/.cxx/Debug/5j6f4z1l/arm64-v8a", "source": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}