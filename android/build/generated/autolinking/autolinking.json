{"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank", "reactNativePath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-community/netinfo": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo", "name": "@react-native-community/netinfo", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android", "packageImportPath": "import com.reactnativecommunity.netinfo.NetInfoPackage;", "packageInstance": "new NetInfoPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/@react-native-community/netinfo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo", "name": "expo", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo/android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-google-mobile-ads": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads", "name": "react-native-google-mobile-ads", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android", "packageImportPath": "import io.invertase.googlemobileads.ReactNativeGoogleMobileAdsPackage;", "packageInstance": "new ReactNativeGoogleMobileAdsPackage()", "buildTypes": [], "libraryName": "RNGoogleMobileAdsSpec", "componentDescriptors": ["RNGoogleMobileAdsNativeViewComponentDescriptor", "RNGoogleMobileAdsMediaViewComponentDescriptor", "RNGoogleMobileAdsBannerViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-mmkv": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv", "name": "react-native-mmkv", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android", "packageImportPath": "import com.mrousavy.mmkv.MmkvPackage;", "packageInstance": "new MmkvPackage()", "buildTypes": [], "libraryName": "RNMmkvSpec", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": "react-native-mmkv", "cxxModuleCMakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-mmkv/android/CMakeLists.txt", "cxxModuleHeaderName": "NativeMmkvModule"}}}, "react-native-reanimated": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-vector-icons": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons", "name": "react-native-vector-icons", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android", "packageImportPath": "import com.oblador.vectoricons.VectorIconsPackage;", "packageInstance": "new VectorIconsPackage()", "buildTypes": [], "libraryName": "RNVectorIconsSpec", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.fazlerabbistat.BCSQuestionBank", "sourceDir": "/Users/<USER>/Development/expo/BCS Question Bank/BCS-Question-Bank/android"}}}