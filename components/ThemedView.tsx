import { StyleSheet, View, type ViewProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'surface' | 'surfaceSecondary' | 'surfaceTertiary' | 'card' | 'elevated';
};

export function ThemedView({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...otherProps
}: ThemedViewProps) {
  // Determine the appropriate background color based on view type
  let colorKey: 'background' | 'backgroundSecondary' | 'backgroundTertiary' | 'surface' | 'surfaceSecondary' | 'surfaceTertiary' = 'background';

  switch (type) {
    case 'surface':
    case 'card':
      colorKey = 'surface';
      break;
    case 'surfaceSecondary':
      colorKey = 'surfaceSecondary';
      break;
    case 'surfaceTertiary':
      colorKey = 'surfaceTertiary';
      break;
    case 'elevated':
      colorKey = 'surface';
      break;
    default:
      colorKey = 'background';
  }

  const backgroundColor = useThemeColor({ light: lightColor, dark: darkColor }, colorKey);
  const shadowColor = useThemeColor({}, 'shadow');
  const borderColor = useThemeColor({}, 'border');

  // Apply additional styling based on type
  const typeStyles = type === 'card' || type === 'elevated' ? {
    shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderRadius: 12,
  } : type === 'surface' || type === 'surfaceSecondary' || type === 'surfaceTertiary' ? {
    borderColor,
    borderWidth: StyleSheet.hairlineWidth,
  } : {};

  return (
    <View
      style={[
        // { backgroundColor },
        typeStyles,
        style
      ]}
      {...otherProps}
    />
  );
}
