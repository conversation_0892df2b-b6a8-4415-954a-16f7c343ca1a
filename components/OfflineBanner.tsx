import * as Network from 'expo-network';
import React, { useEffect, useState } from 'react';
import { Animated, StyleSheet, Text } from 'react-native';

interface OfflineBannerProps {
  isVisible: boolean;
}

// Hook to use Expo Network for network status detection
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    let subscription: any;

    const checkNetworkStatus = async () => {
      try {
        const networkState = await Network.getNetworkStateAsync();
        const online = networkState.isConnected && networkState.isInternetReachable;
        setIsOnline(online ?? false);

        console.log('🌐 Network Status:', {
          type: networkState.type,
          isConnected: networkState.isConnected,
          isInternetReachable: networkState.isInternetReachable,
          online: online
        });
      } catch (error) {
        console.error('Error checking network status:', error);
        setIsOnline(false);
      }
    };

    // Initial check
    checkNetworkStatus();

    // Set up listener for network changes
    subscription = Network.addNetworkStateListener((networkState) => {
      const online = networkState.isConnected && networkState.isInternetReachable;
      setIsOnline(online ?? false);

      console.log('🌐 Network Status Changed:', {
        type: networkState.type,
        isConnected: networkState.isConnected,
        isInternetReachable: networkState.isInternetReachable,
        online: online
      });
    });

    return () => {
      if (subscription) {
        subscription.remove();
      }
    };
  }, []);

  return { isOnline };
};

export const OfflineBanner: React.FC<OfflineBannerProps> = ({ isVisible }) => {
  const { useThemeColor } = require('@/hooks/useThemeColor');
  const errorColor = useThemeColor({}, 'error');
  const textInverseColor = useThemeColor({}, 'textInverse');

  const [slideAnim] = useState(new Animated.Value(100)); // Start below screen
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setShowBanner(true);
      // Slide up animation
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Slide down animation
      Animated.timing(slideAnim, {
        toValue: 100,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setShowBanner(false);
      });
    }
  }, [isVisible, slideAnim]);

  if (!showBanner) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: errorColor,
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      <Text style={[styles.message, { color: textInverseColor }]}>
        You&apos;re offline. Some features may not work properly.
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: 5,
    paddingHorizontal: 16,
    paddingBottom: 16, // Extra padding for safe area
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 8,
    zIndex: 1000,
  },
  message: {
    fontSize: 13,
    textAlign: 'center',
    lineHeight: 13,
  },
});

export default OfflineBanner;
