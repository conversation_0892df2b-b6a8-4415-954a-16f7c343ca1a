import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { StyleSheet } from 'react-native';
import { ProgressBar } from 'react-native-paper';

interface AutoDownloadStatusProps {
  status: {
    isRunning: boolean;
    current: number;
    total: number;
    currentSet: string;
    completed: boolean;
    errors: string[];
  };
  onDismiss?: () => void;
}

export const AutoDownloadStatus: React.FC<AutoDownloadStatusProps> = ({
  status,
  onDismiss
}) => {
  // Always call hooks first (before any early returns)
  const surfaceColor = useThemeColor({}, 'surface');
  const primaryColor = useThemeColor({}, 'primary');
  const textColor = useThemeColor({}, 'text');
  const successColor = useThemeColor({}, 'success');
  const errorColor = useThemeColor({}, 'error');

  // Auto-dismiss after completion
  React.useEffect(() => {
    if (status.completed && !status.isRunning) {
      const timer = setTimeout(() => {
        onDismiss?.();
      }, 3000); // Auto-dismiss after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [status.completed, status.isRunning, onDismiss]);

  // Don't show if not running and not completed
  if (!status.isRunning && !status.completed && status.errors.length === 0) {
    return null;
  }

  const progress = status.total > 0 ? status.current / status.total : 0;

  return (
    <ThemedView style={[styles.container, { backgroundColor: surfaceColor }]}>
      <ThemedView style={styles.content}>
        {status.isRunning && (
          <>
            <ThemedView style={styles.header}>
              <ThemedText style={[styles.title, { color: textColor }]}>📥 Downloading Questions</ThemedText>
              <ThemedText style={[styles.counter, { color: primaryColor, backgroundColor: primaryColor + '20' }]}>
                {status.current}/{status.total}
              </ThemedText>
            </ThemedView>

            <ProgressBar
              progress={progress}
              color={primaryColor}
              style={styles.progressBar}
            />

            <ThemedText style={[styles.currentSet, { color: textColor }]} numberOfLines={1}>
              {status.currentSet}
            </ThemedText>
          </>
        )}

        {status.completed && !status.isRunning && (
          <ThemedView style={styles.completedContainer}>
            <ThemedText style={[styles.completedText, { color: successColor }]}>
              ✅ Download Complete! All questions are now available offline.
            </ThemedText>
          </ThemedView>
        )}

        {status.errors.length > 0 && (
          <ThemedView style={[styles.errorsContainer, { backgroundColor: errorColor + '10', borderLeftColor: errorColor }]}>
            <ThemedText style={[styles.errorTitle, { color: errorColor }]}>
              ⚠️ Some downloads failed:
            </ThemedText>
            {status.errors.slice(0, 2).map((error, index) => (
              <ThemedText key={index} style={[styles.errorText, { color: errorColor }]} numberOfLines={1}>
                • {error}
              </ThemedText>
            ))}
            {status.errors.length > 2 && (
              <ThemedText style={[styles.errorText, { color: errorColor }]}>
                ... and {status.errors.length - 2} more
              </ThemedText>
            )}
          </ThemedView>
        )}
      </ThemedView>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    marginTop: 8,
    borderRadius: 12,
    elevation: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  content: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  counter: {
    fontSize: 14,
    fontWeight: '500',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    marginBottom: 8,
  },
  currentSet: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  completedContainer: {
    alignItems: 'center',
    paddingVertical: 4,
  },
  completedText: {
    fontSize: 15,
    fontWeight: '500',
    textAlign: 'center',
  },
  errorsContainer: {
    marginTop: 8,
    padding: 8,
    borderRadius: 8,
    borderLeftWidth: 3,
  },
  errorTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 13,
    marginBottom: 2,
  },
});

export default AutoDownloadStatus;
