import React from 'react';
import { StyleSheet, View } from 'react-native';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';

interface BannerAdComponentProps {
  size?: BannerAdSize;
  style?: any;
}

export const BannerAdComponent: React.FC<BannerAdComponentProps> = ({ 
  size = BannerAdSize.BANNER,
  style 
}) => {
  // Use test ad unit ID for development, replace with your actual ad unit ID for production
  const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-4790670410909128/6213280472';

  return (
    <View style={[styles.container, style]}>
      <BannerAd
        unitId={adUnitId}
        size={size}
        requestOptions={{
          requestNonPersonalizedAdsOnly: true,
        }}
        onAdLoaded={() => {
          console.log('Banner ad loaded');
        }}
        onAdFailedToLoad={(error) => {
          console.log('Banner ad failed to load:', error);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
});
