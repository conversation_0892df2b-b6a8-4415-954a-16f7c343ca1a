import { useEffect, useState } from 'react';
import { AdEventType, InterstitialAd, TestIds } from 'react-native-google-mobile-ads';

// Use test ad unit ID for development, replace with your actual ad unit ID for production
const adUnitId = __DEV__ ? TestIds.INTERSTITIAL : 'ca-app-pub-4790670410909128/7077701782';

const interstitial = InterstitialAd.createForAdUnitId(adUnitId, {
  requestNonPersonalizedAdsOnly: true,
});

export const useInterstitialAd = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const unsubscribeLoaded = interstitial.addAdEventListener(AdEventType.LOADED, () => {
      setIsLoaded(true);
      setIsLoading(false);
      console.log('Interstitial ad loaded');
    });

    const unsubscribeError = interstitial.addAdEventListener(AdEventType.ERROR, (error) => {
      setIsLoading(false);
      console.log('Interstitial ad error:', error);
    });

    const unsubscribeClosed = interstitial.addAdEventListener(AdEventType.CLOSED, () => {
      setIsLoaded(false);
      console.log('Interstitial ad closed');
      // Load a new ad for next time
      loadAd();
    });

    // Load the initial ad
    loadAd();

    return () => {
      unsubscribeLoaded();
      unsubscribeError();
      unsubscribeClosed();
    };
  }, []);

  const loadAd = () => {
    if (!isLoading && !isLoaded) {
      setIsLoading(true);
      interstitial.load();
    }
  };

  const showAd = () => {
    if (isLoaded) {
      interstitial.show();
    } else {
      console.log('Interstitial ad not ready yet');
      // Optionally load a new ad
      loadAd();
    }
  };

  return {
    isLoaded,
    isLoading,
    showAd,
    loadAd,
  };
};
