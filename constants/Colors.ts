/**
 * Modern color palette for BCS Question Bank app with comprehensive dark theme support.
 * Colors are designed for optimal readability and modern aesthetics in both light and dark modes.
 */

// Primary brand colors
const primaryLight = '#667eea';
const primaryDark = '#818cf8';

// Secondary accent colors
const secondaryLight = '#f59e0b';
const secondaryDark = '#fbbf24';

// Success/Error colors
const successLight = '#10b981';
const successDark = '#34d399';
const errorLight = '#ef4444';
const errorDark = '#f87171';

export const Colors = {
  light: {
    // Text colors
    text: '#1e293b',
    textSecondary: '#64748b',
    textMuted: '#94a3b8',
    textInverse: '#ffffff',

    // Background colors
    background: '#ffffff',
    backgroundSecondary: '#f8fafc',
    backgroundTertiary: '#f1f5f9',

    // Surface colors (for cards, modals, etc.)
    surface: '#ffffff',
    surfaceSecondary: '#f8fafc',
    surfaceTertiary: '#f1f5f9',

    // Border colors
    border: '#e2e8f0',
    borderSecondary: '#cbd5e1',

    // Primary colors
    primary: primaryLight,
    primaryLight: '#8b9cf7',
    primaryDark: '#4f46e5',

    // Secondary colors
    secondary: secondaryLight,
    secondaryLight: '#fcd34d',
    secondaryDark: '#d97706',

    // Status colors
    success: successLight,
    successLight: '#6ee7b7',
    successDark: '#059669',
    error: errorLight,
    errorLight: '#fca5a5',
    errorDark: '#dc2626',
    warning: '#f59e0b',
    info: '#3b82f6',

    // Legacy support
    tint: primaryLight,
    icon: '#64748b',
    tabIconDefault: '#94a3b8',
    tabIconSelected: primaryLight,

    // Shadow colors
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowDark: 'rgba(0, 0, 0, 0.2)',
  },
  dark: {
    // Text colors
    text: '#f1f5f9',
    textSecondary: '#cbd5e1',
    textMuted: '#94a3b8',
    textInverse: '#1e293b',

    // Background colors
    background: '#0f172a',
    backgroundSecondary: '#1e293b',
    backgroundTertiary: '#334155',

    // Surface colors (for cards, modals, etc.)
    surface: '#1e293b',
    surfaceSecondary: '#334155',
    surfaceTertiary: '#475569',

    // Border colors
    border: '#334155',
    borderSecondary: '#475569',

    // Primary colors
    primary: primaryDark,
    primaryLight: '#a5b4fc',
    primaryDark: '#6366f1',

    // Secondary colors
    secondary: secondaryDark,
    secondaryLight: '#fed7aa',
    secondaryDark: '#ea580c',

    // Status colors
    success: successDark,
    successLight: '#86efac',
    successDark: '#16a34a',
    error: errorDark,
    errorLight: '#fca5a5',
    errorDark: '#dc2626',
    warning: '#fbbf24',
    info: '#60a5fa',

    // Legacy support
    tint: primaryDark,
    icon: '#cbd5e1',
    tabIconDefault: '#94a3b8',
    tabIconSelected: primaryDark,

    // Shadow colors
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowDark: 'rgba(0, 0, 0, 0.5)',
  },
};
