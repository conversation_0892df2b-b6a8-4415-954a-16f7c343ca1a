/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNGoogleMobileAdsSpec.h"


@implementation NativeAppOpenModuleSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeAppOpenModuleSpecJSI_appOpenLoad(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "appOpenLoad", @selector(appOpenLoad:adUnitId:requestOptions:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeAppOpenModuleSpecJSI_appOpenShow(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "appOpenShow", @selector(appOpenShow:adUnitId:showOptions:resolve:reject:), args, count);
    }

  NativeAppOpenModuleSpecJSI::NativeAppOpenModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["appOpenLoad"] = MethodMetadata {3, __hostFunction_NativeAppOpenModuleSpecJSI_appOpenLoad};
        
        
        methodMap_["appOpenShow"] = MethodMetadata {3, __hostFunction_NativeAppOpenModuleSpecJSI_appOpenShow};
        
  }
} // namespace facebook::react

@implementation NativeConsentModuleSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end

@implementation RCTCxxConvert (NativeConsentModule_AdsConsentInfoOptions)
+ (RCTManagedPointer *)JS_NativeConsentModule_AdsConsentInfoOptions:(id)json
{
  return facebook::react::managedPointer<JS::NativeConsentModule::AdsConsentInfoOptions>(json);
}
@end
namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_requestInfoUpdate(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "requestInfoUpdate", @selector(requestInfoUpdate:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_showForm(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "showForm", @selector(showForm:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_showPrivacyOptionsForm(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "showPrivacyOptionsForm", @selector(showPrivacyOptionsForm:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_loadAndShowConsentFormIfRequired(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "loadAndShowConsentFormIfRequired", @selector(loadAndShowConsentFormIfRequired:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_getConsentInfo(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getConsentInfo", @selector(getConsentInfo:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_getTCString(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getTCString", @selector(getTCString:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_getGdprApplies(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getGdprApplies", @selector(getGdprApplies:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_getPurposeConsents(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getPurposeConsents", @selector(getPurposeConsents:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_getPurposeLegitimateInterests(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getPurposeLegitimateInterests", @selector(getPurposeLegitimateInterests:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeConsentModuleSpecJSI_reset(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "reset", @selector(reset), args, count);
    }

  NativeConsentModuleSpecJSI::NativeConsentModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["requestInfoUpdate"] = MethodMetadata {1, __hostFunction_NativeConsentModuleSpecJSI_requestInfoUpdate};
        setMethodArgConversionSelector(@"requestInfoUpdate", 0, @"JS_NativeConsentModule_AdsConsentInfoOptions:");
        
        methodMap_["showForm"] = MethodMetadata {0, __hostFunction_NativeConsentModuleSpecJSI_showForm};
        
        
        methodMap_["showPrivacyOptionsForm"] = MethodMetadata {0, __hostFunction_NativeConsentModuleSpecJSI_showPrivacyOptionsForm};
        
        
        methodMap_["loadAndShowConsentFormIfRequired"] = MethodMetadata {0, __hostFunction_NativeConsentModuleSpecJSI_loadAndShowConsentFormIfRequired};
        
        
        methodMap_["getConsentInfo"] = MethodMetadata {0, __hostFunction_NativeConsentModuleSpecJSI_getConsentInfo};
        
        
        methodMap_["getTCString"] = MethodMetadata {0, __hostFunction_NativeConsentModuleSpecJSI_getTCString};
        
        
        methodMap_["getGdprApplies"] = MethodMetadata {0, __hostFunction_NativeConsentModuleSpecJSI_getGdprApplies};
        
        
        methodMap_["getPurposeConsents"] = MethodMetadata {0, __hostFunction_NativeConsentModuleSpecJSI_getPurposeConsents};
        
        
        methodMap_["getPurposeLegitimateInterests"] = MethodMetadata {0, __hostFunction_NativeConsentModuleSpecJSI_getPurposeLegitimateInterests};
        
        
        methodMap_["reset"] = MethodMetadata {0, __hostFunction_NativeConsentModuleSpecJSI_reset};
        
  }
} // namespace facebook::react

@implementation NativeGoogleMobileAdsModuleSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_initialize(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "initialize", @selector(initialize:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_setRequestConfiguration(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "setRequestConfiguration", @selector(setRequestConfiguration:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_openAdInspector(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "openAdInspector", @selector(openAdInspector:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_openDebugMenu(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "openDebugMenu", @selector(openDebugMenu:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_setAppVolume(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setAppVolume", @selector(setAppVolume:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_setAppMuted(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setAppMuted", @selector(setAppMuted:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
    }

  NativeGoogleMobileAdsModuleSpecJSI::NativeGoogleMobileAdsModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["initialize"] = MethodMetadata {0, __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_initialize};
        
        
        methodMap_["setRequestConfiguration"] = MethodMetadata {1, __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_setRequestConfiguration};
        
        
        methodMap_["openAdInspector"] = MethodMetadata {0, __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_openAdInspector};
        
        
        methodMap_["openDebugMenu"] = MethodMetadata {1, __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_openDebugMenu};
        
        
        methodMap_["setAppVolume"] = MethodMetadata {1, __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_setAppVolume};
        
        
        methodMap_["setAppMuted"] = MethodMetadata {1, __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_setAppMuted};
        
        
        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeGoogleMobileAdsModuleSpecJSI_getConstants};
        
  }
} // namespace facebook::react

@implementation NativeGoogleMobileAdsNativeModuleSpecBase
- (void)emitOnAdEvent:(NSDictionary *)value
{
  _eventEmitterCallback("onAdEvent", value);
}

- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeGoogleMobileAdsNativeModuleSpecJSI_load(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "load", @selector(load:requestOptions:resolve:reject:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeGoogleMobileAdsNativeModuleSpecJSI_destroy(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "destroy", @selector(destroy:), args, count);
    }

  NativeGoogleMobileAdsNativeModuleSpecJSI::NativeGoogleMobileAdsNativeModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["load"] = MethodMetadata {2, __hostFunction_NativeGoogleMobileAdsNativeModuleSpecJSI_load};
        
        
        methodMap_["destroy"] = MethodMetadata {1, __hostFunction_NativeGoogleMobileAdsNativeModuleSpecJSI_destroy};
        
        eventEmitterMap_["onAdEvent"] = std::make_shared<AsyncEventEmitter<id>>();
        setEventEmitterCallback([&](const std::string &name, id value) {
          static_cast<AsyncEventEmitter<id> &>(*eventEmitterMap_[name]).emit(value);
        });
  }
} // namespace facebook::react

@implementation NativeInterstitialModuleSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeInterstitialModuleSpecJSI_interstitialLoad(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "interstitialLoad", @selector(interstitialLoad:adUnitId:requestOptions:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeInterstitialModuleSpecJSI_interstitialShow(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "interstitialShow", @selector(interstitialShow:adUnitId:showOptions:resolve:reject:), args, count);
    }

  NativeInterstitialModuleSpecJSI::NativeInterstitialModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["interstitialLoad"] = MethodMetadata {3, __hostFunction_NativeInterstitialModuleSpecJSI_interstitialLoad};
        
        
        methodMap_["interstitialShow"] = MethodMetadata {3, __hostFunction_NativeInterstitialModuleSpecJSI_interstitialShow};
        
  }
} // namespace facebook::react

@implementation NativeRewardedInterstitialModuleSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeRewardedInterstitialModuleSpecJSI_rewardedInterstitialLoad(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "rewardedInterstitialLoad", @selector(rewardedInterstitialLoad:adUnitId:requestOptions:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRewardedInterstitialModuleSpecJSI_rewardedInterstitialShow(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "rewardedInterstitialShow", @selector(rewardedInterstitialShow:adUnitId:showOptions:resolve:reject:), args, count);
    }

  NativeRewardedInterstitialModuleSpecJSI::NativeRewardedInterstitialModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["rewardedInterstitialLoad"] = MethodMetadata {3, __hostFunction_NativeRewardedInterstitialModuleSpecJSI_rewardedInterstitialLoad};
        
        
        methodMap_["rewardedInterstitialShow"] = MethodMetadata {3, __hostFunction_NativeRewardedInterstitialModuleSpecJSI_rewardedInterstitialShow};
        
  }
} // namespace facebook::react

@implementation NativeRewardedModuleSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeRewardedModuleSpecJSI_rewardedLoad(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "rewardedLoad", @selector(rewardedLoad:adUnitId:requestOptions:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRewardedModuleSpecJSI_rewardedShow(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "rewardedShow", @selector(rewardedShow:adUnitId:showOptions:resolve:reject:), args, count);
    }

  NativeRewardedModuleSpecJSI::NativeRewardedModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["rewardedLoad"] = MethodMetadata {3, __hostFunction_NativeRewardedModuleSpecJSI_rewardedLoad};
        
        
        methodMap_["rewardedShow"] = MethodMetadata {3, __hostFunction_NativeRewardedModuleSpecJSI_rewardedShow};
        
  }
} // namespace facebook::react
