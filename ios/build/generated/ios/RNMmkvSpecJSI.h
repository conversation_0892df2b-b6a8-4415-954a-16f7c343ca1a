/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


#pragma mark - NativeMmkvMode

enum class NativeMmkvMode { SINGLE_PROCESS, MULTI_PROCESS };

template <>
struct Bridging<NativeMmkvMode> {
  static NativeMmkvMode fromJs(jsi::Runtime &rt, const jsi::String &rawValue) {
    std::string value = rawValue.utf8(rt);
    if (value == "SINGLE_PROCESS") {
      return NativeMmkvMode::SINGLE_PROCESS;
    } else if (value == "MULTI_PROCESS") {
      return NativeMmkvMode::MULTI_PROCESS;
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for value");
    }
  }

  static jsi::String toJs(jsi::Runtime &rt, NativeMmkvMode value) {
    if (value == NativeMmkvMode::SINGLE_PROCESS) {
      return bridging::toJs(rt, "SINGLE_PROCESS");
    } else if (value == NativeMmkvMode::MULTI_PROCESS) {
      return bridging::toJs(rt, "MULTI_PROCESS");
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for enum value");
    }
  }
};
  
#pragma mark - NativeMmkvConfiguration

template <typename P0, typename P1, typename P2, typename P3, typename P4>
struct NativeMmkvConfiguration {
  P0 id;
  P1 path;
  P2 encryptionKey;
  P3 mode;
  P4 readOnly;
  bool operator==(const NativeMmkvConfiguration &other) const {
    return id == other.id && path == other.path && encryptionKey == other.encryptionKey && mode == other.mode && readOnly == other.readOnly;
  }
};

template <typename T>
struct NativeMmkvConfigurationBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.id)>(rt, value.getProperty(rt, "id"), jsInvoker),
      bridging::fromJs<decltype(types.path)>(rt, value.getProperty(rt, "path"), jsInvoker),
      bridging::fromJs<decltype(types.encryptionKey)>(rt, value.getProperty(rt, "encryptionKey"), jsInvoker),
      bridging::fromJs<decltype(types.mode)>(rt, value.getProperty(rt, "mode"), jsInvoker),
      bridging::fromJs<decltype(types.readOnly)>(rt, value.getProperty(rt, "readOnly"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String idToJs(jsi::Runtime &rt, decltype(types.id) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String pathToJs(jsi::Runtime &rt, decltype(types.path) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String encryptionKeyToJs(jsi::Runtime &rt, decltype(types.encryptionKey) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String modeToJs(jsi::Runtime &rt, decltype(types.mode) value) {
    return bridging::toJs(rt, value);
  }

  static bool readOnlyToJs(jsi::Runtime &rt, decltype(types.readOnly) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "id", bridging::toJs(rt, value.id, jsInvoker));
    if (value.path) {
      result.setProperty(rt, "path", bridging::toJs(rt, value.path.value(), jsInvoker));
    }
    if (value.encryptionKey) {
      result.setProperty(rt, "encryptionKey", bridging::toJs(rt, value.encryptionKey.value(), jsInvoker));
    }
    if (value.mode) {
      result.setProperty(rt, "mode", bridging::toJs(rt, value.mode.value(), jsInvoker));
    }
    if (value.readOnly) {
      result.setProperty(rt, "readOnly", bridging::toJs(rt, value.readOnly.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeMmkvCxxSpecJSI : public TurboModule {
protected:
  NativeMmkvCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual bool initialize(jsi::Runtime &rt, jsi::String basePath) = 0;
  virtual jsi::Object createMMKV(jsi::Runtime &rt, jsi::Object configuration) = 0;

};

template <typename T>
class JSI_EXPORT NativeMmkvCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "MmkvCxx";

protected:
  NativeMmkvCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeMmkvCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeMmkvCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeMmkvCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    bool initialize(jsi::Runtime &rt, jsi::String basePath) override {
      static_assert(
          bridging::getParameterCount(&T::initialize) == 2,
          "Expected initialize(...) to have 2 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::initialize, jsInvoker_, instance_, std::move(basePath));
    }
    jsi::Object createMMKV(jsi::Runtime &rt, jsi::Object configuration) override {
      static_assert(
          bridging::getParameterCount(&T::createMMKV) == 2,
          "Expected createMMKV(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::createMMKV, jsInvoker_, instance_, std::move(configuration));
    }

  private:
    friend class NativeMmkvCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeMmkvPlatformContextCxxSpecJSI : public TurboModule {
protected:
  NativeMmkvPlatformContextCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::String getBaseDirectory(jsi::Runtime &rt) = 0;
  virtual std::optional<jsi::String> getAppGroupDirectory(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeMmkvPlatformContextCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "MmkvPlatformContext";

protected:
  NativeMmkvPlatformContextCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeMmkvPlatformContextCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeMmkvPlatformContextCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeMmkvPlatformContextCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::String getBaseDirectory(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getBaseDirectory) == 1,
          "Expected getBaseDirectory(...) to have 1 parameters");

      return bridging::callFromJs<jsi::String>(
          rt, &T::getBaseDirectory, jsInvoker_, instance_);
    }
    std::optional<jsi::String> getAppGroupDirectory(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getAppGroupDirectory) == 1,
          "Expected getAppGroupDirectory(...) to have 1 parameters");

      return bridging::callFromJs<std::optional<jsi::String>>(
          rt, &T::getAppGroupDirectory, jsInvoker_, instance_);
    }

  private:
    friend class NativeMmkvPlatformContextCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
