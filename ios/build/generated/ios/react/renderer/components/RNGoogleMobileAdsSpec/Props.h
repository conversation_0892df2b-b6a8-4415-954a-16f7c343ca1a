
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>
#include <vector>

namespace facebook::react {

struct RNGoogleMobileAdsBannerViewSizeConfigStruct {
  std::vector<std::string> sizes{};
  Float maxHeight{0.0};
  Float width{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNGoogleMobileAdsBannerViewSizeConfigStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_sizes = map.find("sizes");
  if (tmp_sizes != map.end()) {
    fromRawValue(context, tmp_sizes->second, result.sizes);
  }
  auto tmp_maxHeight = map.find("maxHeight");
  if (tmp_maxHeight != map.end()) {
    fromRawValue(context, tmp_maxHeight->second, result.maxHeight);
  }
  auto tmp_width = map.find("width");
  if (tmp_width != map.end()) {
    fromRawValue(context, tmp_width->second, result.width);
  }
}

static inline std::string toString(const RNGoogleMobileAdsBannerViewSizeConfigStruct &value) {
  return "[Object RNGoogleMobileAdsBannerViewSizeConfigStruct]";
}
class RNGoogleMobileAdsBannerViewProps final : public ViewProps {
 public:
  RNGoogleMobileAdsBannerViewProps() = default;
  RNGoogleMobileAdsBannerViewProps(const PropsParserContext& context, const RNGoogleMobileAdsBannerViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  RNGoogleMobileAdsBannerViewSizeConfigStruct sizeConfig{};
  std::string unitId{};
  std::string request{};
  bool manualImpressionsEnabled{false};
};

class RNGoogleMobileAdsMediaViewProps final : public ViewProps {
 public:
  RNGoogleMobileAdsMediaViewProps() = default;
  RNGoogleMobileAdsMediaViewProps(const PropsParserContext& context, const RNGoogleMobileAdsMediaViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string responseId{};
  std::string resizeMode{};
};

class RNGoogleMobileAdsNativeViewProps final : public ViewProps {
 public:
  RNGoogleMobileAdsNativeViewProps() = default;
  RNGoogleMobileAdsNativeViewProps(const PropsParserContext& context, const RNGoogleMobileAdsNativeViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::string responseId{};
};

} // namespace facebook::react
