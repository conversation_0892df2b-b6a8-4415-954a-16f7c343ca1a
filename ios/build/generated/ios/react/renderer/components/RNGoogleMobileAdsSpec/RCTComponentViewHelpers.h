/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GenerateComponentHObjCpp.js
*/

#import <Foundation/Foundation.h>
#import <React/RCTDefines.h>
#import <React/RCTLog.h>

NS_ASSUME_NONNULL_BEGIN

@protocol RCTRNGoogleMobileAdsBannerViewViewProtocol <NSObject>
- (void)recordManualImpression;
- (void)load;
@end

RCT_EXTERN inline void RCTRNGoogleMobileAdsBannerViewHandleCommand(
  id<RCTRNGoogleMobileAdsBannerViewViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"recordManualImpression"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNGoogleMobileAdsBannerView", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView recordManualImpression];
  return;
}

if ([commandName isEqualToString:@"load"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNGoogleMobileAdsBannerView", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView load];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"RNGoogleMobileAdsBannerView", commandName);
#endif
}

@protocol RCTRNGoogleMobileAdsMediaViewViewProtocol <NSObject>

@end

@protocol RCTRNGoogleMobileAdsNativeViewViewProtocol <NSObject>
- (void)registerAsset:(NSString *)assetType reactTag:(NSInteger)reactTag;
@end

RCT_EXTERN inline void RCTRNGoogleMobileAdsNativeViewHandleCommand(
  id<RCTRNGoogleMobileAdsNativeViewViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"registerAsset"]) {
#if RCT_DEBUG
  if ([args count] != 2) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNGoogleMobileAdsNativeView", commandName, (int)[args count], 2);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNGoogleMobileAdsNativeView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * assetType = (NSString *)arg0;

NSObject *arg1 = args[1];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg1, [NSNumber class], @"number", @"RNGoogleMobileAdsNativeView", commandName, @"2nd")) {
    return;
  }
#endif
  NSInteger reactTag = [(NSNumber *)arg1 intValue];

  [componentView registerAsset:assetType reactTag:reactTag];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"RNGoogleMobileAdsNativeView", commandName);
#endif
}

NS_ASSUME_NONNULL_END