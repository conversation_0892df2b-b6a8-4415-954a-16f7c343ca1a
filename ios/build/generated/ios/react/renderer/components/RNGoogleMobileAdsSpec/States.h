/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateStateH.js
 */
#pragma once

#ifdef ANDROID
#include <folly/dynamic.h>
#endif

namespace facebook::react {

class RNGoogleMobileAdsBannerViewState {
public:
  RNGoogleMobileAdsBannerViewState() = default;

#ifdef ANDROID
  RNGoogleMobileAdsBannerViewState(RNGoogleMobileAdsBannerViewState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNGoogleMobileAdsMediaViewState {
public:
  RNGoogleMobileAdsMediaViewState() = default;

#ifdef ANDROID
  RNGoogleMobileAdsMediaViewState(RNGoogleMobileAdsMediaViewState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

class RNGoogleMobileAdsNativeViewState {
public:
  RNGoogleMobileAdsNativeViewState() = default;

#ifdef ANDROID
  RNGoogleMobileAdsNativeViewState(RNGoogleMobileAdsNativeViewState const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};

} // namespace facebook::react