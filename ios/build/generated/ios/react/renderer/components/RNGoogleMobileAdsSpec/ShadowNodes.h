
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.h>
#include <react/renderer/components/RNGoogleMobileAdsSpec/Props.h>
#include <react/renderer/components/RNGoogleMobileAdsSpec/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char RNGoogleMobileAdsBannerViewComponentName[];

/*
 * `ShadowNode` for <RNGoogleMobileAdsBannerView> component.
 */
using RNGoogleMobileAdsBannerViewShadowNode = ConcreteViewShadowNode<
    RNGoogleMobileAdsBannerViewComponentName,
    RNGoogleMobileAdsBannerViewProps,
    RNGoogleMobileAdsBannerViewEventEmitter,
    RNGoogleMobileAdsBannerViewState>;

JSI_EXPORT extern const char RNGoogleMobileAdsMediaViewComponentName[];

/*
 * `ShadowNode` for <RNGoogleMobileAdsMediaView> component.
 */
using RNGoogleMobileAdsMediaViewShadowNode = ConcreteViewShadowNode<
    RNGoogleMobileAdsMediaViewComponentName,
    RNGoogleMobileAdsMediaViewProps,
    RNGoogleMobileAdsMediaViewEventEmitter,
    RNGoogleMobileAdsMediaViewState>;

JSI_EXPORT extern const char RNGoogleMobileAdsNativeViewComponentName[];

/*
 * `ShadowNode` for <RNGoogleMobileAdsNativeView> component.
 */
using RNGoogleMobileAdsNativeViewShadowNode = ConcreteViewShadowNode<
    RNGoogleMobileAdsNativeViewComponentName,
    RNGoogleMobileAdsNativeViewProps,
    RNGoogleMobileAdsNativeViewEventEmitter,
    RNGoogleMobileAdsNativeViewState>;

} // namespace facebook::react
