
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterH.js
 */
#pragma once

#include <react/renderer/components/view/ViewEventEmitter.h>


namespace facebook::react {
class RNCWebViewEventEmitter : public ViewEventEmitter {
 public:
  using ViewEventEmitter::ViewEventEmitter;

  struct OnContentSizeChange {
      std::string url;
    bool loading;
    std::string title;
    bool canGoBack;
    bool canGoForward;
    double lockIdentifier;
    };

  struct OnRenderProcessGone {
      bool didCrash;
    };

  struct OnContentProcessDidTerminate {
      std::string url;
    bool loading;
    std::string title;
    bool canGoBack;
    bool canGoForward;
    double lockIdentifier;
    };

  struct OnCustomMenuSelection {
      std::string label;
    std::string key;
    std::string selectedText;
    };

  struct OnFileDownload {
      std::string downloadUrl;
    };

  struct OnLoadingError {
      std::string url;
    bool loading;
    std::string title;
    bool canGoBack;
    bool canGoForward;
    double lockIdentifier;
    std::string domain;
    int code;
    std::string description;
    };

  enum class OnLoadingFinishNavigationType {
    Click,
    Formsubmit,
    Backforward,
    Reload,
    Formresubmit,
    Other
  };

  static char const *toString(const OnLoadingFinishNavigationType value) {
    switch (value) {
      case OnLoadingFinishNavigationType::Click: return "click";
      case OnLoadingFinishNavigationType::Formsubmit: return "formsubmit";
      case OnLoadingFinishNavigationType::Backforward: return "backforward";
      case OnLoadingFinishNavigationType::Reload: return "reload";
      case OnLoadingFinishNavigationType::Formresubmit: return "formresubmit";
      case OnLoadingFinishNavigationType::Other: return "other";
    }
  }

  struct OnLoadingFinish {
      std::string url;
    bool loading;
    std::string title;
    bool canGoBack;
    bool canGoForward;
    double lockIdentifier;
    OnLoadingFinishNavigationType navigationType;
    std::string mainDocumentURL;
    };

  struct OnLoadingProgress {
      std::string url;
    bool loading;
    std::string title;
    bool canGoBack;
    bool canGoForward;
    double lockIdentifier;
    double progress;
    };

  enum class OnLoadingStartNavigationType {
    Click,
    Formsubmit,
    Backforward,
    Reload,
    Formresubmit,
    Other
  };

  static char const *toString(const OnLoadingStartNavigationType value) {
    switch (value) {
      case OnLoadingStartNavigationType::Click: return "click";
      case OnLoadingStartNavigationType::Formsubmit: return "formsubmit";
      case OnLoadingStartNavigationType::Backforward: return "backforward";
      case OnLoadingStartNavigationType::Reload: return "reload";
      case OnLoadingStartNavigationType::Formresubmit: return "formresubmit";
      case OnLoadingStartNavigationType::Other: return "other";
    }
  }

  struct OnLoadingStart {
      std::string url;
    bool loading;
    std::string title;
    bool canGoBack;
    bool canGoForward;
    double lockIdentifier;
    OnLoadingStartNavigationType navigationType;
    std::string mainDocumentURL;
    };

  struct OnHttpError {
      std::string url;
    bool loading;
    std::string title;
    bool canGoBack;
    bool canGoForward;
    double lockIdentifier;
    std::string description;
    int statusCode;
    };

  struct OnMessage {
      std::string url;
    bool loading;
    std::string title;
    bool canGoBack;
    bool canGoForward;
    double lockIdentifier;
    std::string data;
    };

  struct OnOpenWindow {
      std::string targetUrl;
    };

  struct OnScrollContentInset {
      double bottom;
    double left;
    double right;
    double top;
    };

  struct OnScrollContentOffset {
      double y;
    double x;
    };

  struct OnScrollContentSize {
      double height;
    double width;
    };

  struct OnScrollLayoutMeasurement {
      double height;
    double width;
    };

  struct OnScrollTargetContentOffset {
      double y;
    double x;
    };

  struct OnScrollVelocity {
      double y;
    double x;
    };

  struct OnScroll {
      OnScrollContentInset contentInset;
    OnScrollContentOffset contentOffset;
    OnScrollContentSize contentSize;
    OnScrollLayoutMeasurement layoutMeasurement;
    OnScrollTargetContentOffset targetContentOffset;
    OnScrollVelocity velocity;
    double zoomScale;
    bool responderIgnoreScroll;
    };

  enum class OnShouldStartLoadWithRequestNavigationType {
    Click,
    Formsubmit,
    Backforward,
    Reload,
    Formresubmit,
    Other
  };

  static char const *toString(const OnShouldStartLoadWithRequestNavigationType value) {
    switch (value) {
      case OnShouldStartLoadWithRequestNavigationType::Click: return "click";
      case OnShouldStartLoadWithRequestNavigationType::Formsubmit: return "formsubmit";
      case OnShouldStartLoadWithRequestNavigationType::Backforward: return "backforward";
      case OnShouldStartLoadWithRequestNavigationType::Reload: return "reload";
      case OnShouldStartLoadWithRequestNavigationType::Formresubmit: return "formresubmit";
      case OnShouldStartLoadWithRequestNavigationType::Other: return "other";
    }
  }

  struct OnShouldStartLoadWithRequest {
      std::string url;
    bool loading;
    std::string title;
    bool canGoBack;
    bool canGoForward;
    double lockIdentifier;
    OnShouldStartLoadWithRequestNavigationType navigationType;
    std::string mainDocumentURL;
    bool isTopFrame;
    };
  void onContentSizeChange(OnContentSizeChange value) const;

  void onRenderProcessGone(OnRenderProcessGone value) const;

  void onContentProcessDidTerminate(OnContentProcessDidTerminate value) const;

  void onCustomMenuSelection(OnCustomMenuSelection value) const;

  void onFileDownload(OnFileDownload value) const;

  void onLoadingError(OnLoadingError value) const;

  void onLoadingFinish(OnLoadingFinish value) const;

  void onLoadingProgress(OnLoadingProgress value) const;

  void onLoadingStart(OnLoadingStart value) const;

  void onHttpError(OnHttpError value) const;

  void onMessage(OnMessage value) const;

  void onOpenWindow(OnOpenWindow value) const;

  void onScroll(OnScroll value) const;

  void onShouldStartLoadWithRequest(OnShouldStartLoadWithRequest value) const;
};
} // namespace facebook::react
