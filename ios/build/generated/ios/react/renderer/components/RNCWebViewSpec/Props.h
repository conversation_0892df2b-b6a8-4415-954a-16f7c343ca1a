
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <cinttypes>
#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>
#include <vector>

namespace facebook::react {

enum class RNCWebViewAndroidLayerType { None, Software, Hardware };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewAndroidLayerType &result) {
  auto string = (std::string)value;
  if (string == "none") { result = RNCWebViewAndroidLayerType::None; return; }
  if (string == "software") { result = RNCWebViewAndroidLayerType::Software; return; }
  if (string == "hardware") { result = RNCWebViewAndroidLayerType::Hardware; return; }
  abort();
}

static inline std::string toString(const RNCWebViewAndroidLayerType &value) {
  switch (value) {
    case RNCWebViewAndroidLayerType::None: return "none";
    case RNCWebViewAndroidLayerType::Software: return "software";
    case RNCWebViewAndroidLayerType::Hardware: return "hardware";
  }
}
enum class RNCWebViewCacheMode { LOAD_DEFAULT, LOAD_CACHE_ELSE_NETWORK, LOAD_NO_CACHE, LOAD_CACHE_ONLY };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewCacheMode &result) {
  auto string = (std::string)value;
  if (string == "LOAD_DEFAULT") { result = RNCWebViewCacheMode::LOAD_DEFAULT; return; }
  if (string == "LOAD_CACHE_ELSE_NETWORK") { result = RNCWebViewCacheMode::LOAD_CACHE_ELSE_NETWORK; return; }
  if (string == "LOAD_NO_CACHE") { result = RNCWebViewCacheMode::LOAD_NO_CACHE; return; }
  if (string == "LOAD_CACHE_ONLY") { result = RNCWebViewCacheMode::LOAD_CACHE_ONLY; return; }
  abort();
}

static inline std::string toString(const RNCWebViewCacheMode &value) {
  switch (value) {
    case RNCWebViewCacheMode::LOAD_DEFAULT: return "LOAD_DEFAULT";
    case RNCWebViewCacheMode::LOAD_CACHE_ELSE_NETWORK: return "LOAD_CACHE_ELSE_NETWORK";
    case RNCWebViewCacheMode::LOAD_NO_CACHE: return "LOAD_NO_CACHE";
    case RNCWebViewCacheMode::LOAD_CACHE_ONLY: return "LOAD_CACHE_ONLY";
  }
}
enum class RNCWebViewMixedContentMode { Never, Always, Compatibility };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewMixedContentMode &result) {
  auto string = (std::string)value;
  if (string == "never") { result = RNCWebViewMixedContentMode::Never; return; }
  if (string == "always") { result = RNCWebViewMixedContentMode::Always; return; }
  if (string == "compatibility") { result = RNCWebViewMixedContentMode::Compatibility; return; }
  abort();
}

static inline std::string toString(const RNCWebViewMixedContentMode &value) {
  switch (value) {
    case RNCWebViewMixedContentMode::Never: return "never";
    case RNCWebViewMixedContentMode::Always: return "always";
    case RNCWebViewMixedContentMode::Compatibility: return "compatibility";
  }
}
enum class RNCWebViewContentInsetAdjustmentBehavior { Never, Automatic, ScrollableAxes, Always };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewContentInsetAdjustmentBehavior &result) {
  auto string = (std::string)value;
  if (string == "never") { result = RNCWebViewContentInsetAdjustmentBehavior::Never; return; }
  if (string == "automatic") { result = RNCWebViewContentInsetAdjustmentBehavior::Automatic; return; }
  if (string == "scrollableAxes") { result = RNCWebViewContentInsetAdjustmentBehavior::ScrollableAxes; return; }
  if (string == "always") { result = RNCWebViewContentInsetAdjustmentBehavior::Always; return; }
  abort();
}

static inline std::string toString(const RNCWebViewContentInsetAdjustmentBehavior &value) {
  switch (value) {
    case RNCWebViewContentInsetAdjustmentBehavior::Never: return "never";
    case RNCWebViewContentInsetAdjustmentBehavior::Automatic: return "automatic";
    case RNCWebViewContentInsetAdjustmentBehavior::ScrollableAxes: return "scrollableAxes";
    case RNCWebViewContentInsetAdjustmentBehavior::Always: return "always";
  }
}
enum class RNCWebViewContentMode { Recommended, Mobile, Desktop };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewContentMode &result) {
  auto string = (std::string)value;
  if (string == "recommended") { result = RNCWebViewContentMode::Recommended; return; }
  if (string == "mobile") { result = RNCWebViewContentMode::Mobile; return; }
  if (string == "desktop") { result = RNCWebViewContentMode::Desktop; return; }
  abort();
}

static inline std::string toString(const RNCWebViewContentMode &value) {
  switch (value) {
    case RNCWebViewContentMode::Recommended: return "recommended";
    case RNCWebViewContentMode::Mobile: return "mobile";
    case RNCWebViewContentMode::Desktop: return "desktop";
  }
}
using RNCWebViewDataDetectorTypesMask = uint32_t;

struct RNCWebViewDataDetectorTypesMaskWrapped {
  RNCWebViewDataDetectorTypesMask value;
};

enum class RNCWebViewDataDetectorTypes: RNCWebViewDataDetectorTypesMask {
  Address = 1 << 0,
  Link = 1 << 1,
  CalendarEvent = 1 << 2,
  TrackingNumber = 1 << 3,
  FlightNumber = 1 << 4,
  LookupSuggestion = 1 << 5,
  PhoneNumber = 1 << 6,
  All = 1 << 7,
  None = 1 << 8
};

constexpr bool operator&(
  RNCWebViewDataDetectorTypesMask const lhs,
  enum RNCWebViewDataDetectorTypes const rhs) {
  return lhs & static_cast<RNCWebViewDataDetectorTypesMask>(rhs);
}

constexpr RNCWebViewDataDetectorTypesMask operator|(
  RNCWebViewDataDetectorTypesMask const lhs,
  enum RNCWebViewDataDetectorTypes const rhs) {
  return lhs | static_cast<RNCWebViewDataDetectorTypesMask>(rhs);
}

constexpr void operator|=(
  RNCWebViewDataDetectorTypesMask &lhs,
  enum RNCWebViewDataDetectorTypes const rhs) {
  lhs = lhs | static_cast<RNCWebViewDataDetectorTypesMask>(rhs);
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewDataDetectorTypesMaskWrapped &wrapped) {
  auto items = std::vector<std::string>{value};
  for (const auto &item : items) {
    if (item == "address") {
      wrapped.value |= RNCWebViewDataDetectorTypes::Address;
      continue;
    }
    if (item == "link") {
      wrapped.value |= RNCWebViewDataDetectorTypes::Link;
      continue;
    }
    if (item == "calendarEvent") {
      wrapped.value |= RNCWebViewDataDetectorTypes::CalendarEvent;
      continue;
    }
    if (item == "trackingNumber") {
      wrapped.value |= RNCWebViewDataDetectorTypes::TrackingNumber;
      continue;
    }
    if (item == "flightNumber") {
      wrapped.value |= RNCWebViewDataDetectorTypes::FlightNumber;
      continue;
    }
    if (item == "lookupSuggestion") {
      wrapped.value |= RNCWebViewDataDetectorTypes::LookupSuggestion;
      continue;
    }
    if (item == "phoneNumber") {
      wrapped.value |= RNCWebViewDataDetectorTypes::PhoneNumber;
      continue;
    }
    if (item == "all") {
      wrapped.value |= RNCWebViewDataDetectorTypes::All;
      continue;
    }
    if (item == "none") {
      wrapped.value |= RNCWebViewDataDetectorTypes::None;
      continue;
    }
    abort();
  }
}

static inline std::string toString(const RNCWebViewDataDetectorTypesMaskWrapped &wrapped) {
    auto result = std::string{};
    auto separator = std::string{", "};

    if (wrapped.value & RNCWebViewDataDetectorTypes::Address) {
      result += "address" + separator;
    }
    if (wrapped.value & RNCWebViewDataDetectorTypes::Link) {
      result += "link" + separator;
    }
    if (wrapped.value & RNCWebViewDataDetectorTypes::CalendarEvent) {
      result += "calendarEvent" + separator;
    }
    if (wrapped.value & RNCWebViewDataDetectorTypes::TrackingNumber) {
      result += "trackingNumber" + separator;
    }
    if (wrapped.value & RNCWebViewDataDetectorTypes::FlightNumber) {
      result += "flightNumber" + separator;
    }
    if (wrapped.value & RNCWebViewDataDetectorTypes::LookupSuggestion) {
      result += "lookupSuggestion" + separator;
    }
    if (wrapped.value & RNCWebViewDataDetectorTypes::PhoneNumber) {
      result += "phoneNumber" + separator;
    }
    if (wrapped.value & RNCWebViewDataDetectorTypes::All) {
      result += "all" + separator;
    }
    if (wrapped.value & RNCWebViewDataDetectorTypes::None) {
      result += "none" + separator;
    }
    if (!result.empty()) {
      result.erase(result.length() - separator.length());
    }
    return result;
}
enum class RNCWebViewMediaCapturePermissionGrantType { Prompt, Grant, Deny, GrantIfSameHostElsePrompt, GrantIfSameHostElseDeny };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewMediaCapturePermissionGrantType &result) {
  auto string = (std::string)value;
  if (string == "prompt") { result = RNCWebViewMediaCapturePermissionGrantType::Prompt; return; }
  if (string == "grant") { result = RNCWebViewMediaCapturePermissionGrantType::Grant; return; }
  if (string == "deny") { result = RNCWebViewMediaCapturePermissionGrantType::Deny; return; }
  if (string == "grantIfSameHostElsePrompt") { result = RNCWebViewMediaCapturePermissionGrantType::GrantIfSameHostElsePrompt; return; }
  if (string == "grantIfSameHostElseDeny") { result = RNCWebViewMediaCapturePermissionGrantType::GrantIfSameHostElseDeny; return; }
  abort();
}

static inline std::string toString(const RNCWebViewMediaCapturePermissionGrantType &value) {
  switch (value) {
    case RNCWebViewMediaCapturePermissionGrantType::Prompt: return "prompt";
    case RNCWebViewMediaCapturePermissionGrantType::Grant: return "grant";
    case RNCWebViewMediaCapturePermissionGrantType::Deny: return "deny";
    case RNCWebViewMediaCapturePermissionGrantType::GrantIfSameHostElsePrompt: return "grantIfSameHostElsePrompt";
    case RNCWebViewMediaCapturePermissionGrantType::GrantIfSameHostElseDeny: return "grantIfSameHostElseDeny";
  }
}
enum class RNCWebViewIndicatorStyle { Default, Black, White };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewIndicatorStyle &result) {
  auto string = (std::string)value;
  if (string == "default") { result = RNCWebViewIndicatorStyle::Default; return; }
  if (string == "black") { result = RNCWebViewIndicatorStyle::Black; return; }
  if (string == "white") { result = RNCWebViewIndicatorStyle::White; return; }
  abort();
}

static inline std::string toString(const RNCWebViewIndicatorStyle &value) {
  switch (value) {
    case RNCWebViewIndicatorStyle::Default: return "default";
    case RNCWebViewIndicatorStyle::Black: return "black";
    case RNCWebViewIndicatorStyle::White: return "white";
  }
}
struct RNCWebViewContentInsetStruct {
  double top{0.0};
  double left{0.0};
  double bottom{0.0};
  double right{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewContentInsetStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_top = map.find("top");
  if (tmp_top != map.end()) {
    fromRawValue(context, tmp_top->second, result.top);
  }
  auto tmp_left = map.find("left");
  if (tmp_left != map.end()) {
    fromRawValue(context, tmp_left->second, result.left);
  }
  auto tmp_bottom = map.find("bottom");
  if (tmp_bottom != map.end()) {
    fromRawValue(context, tmp_bottom->second, result.bottom);
  }
  auto tmp_right = map.find("right");
  if (tmp_right != map.end()) {
    fromRawValue(context, tmp_right->second, result.right);
  }
}

static inline std::string toString(const RNCWebViewContentInsetStruct &value) {
  return "[Object RNCWebViewContentInsetStruct]";
}

struct RNCWebViewMenuItemsStruct {
  std::string label{};
  std::string key{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewMenuItemsStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_label = map.find("label");
  if (tmp_label != map.end()) {
    fromRawValue(context, tmp_label->second, result.label);
  }
  auto tmp_key = map.find("key");
  if (tmp_key != map.end()) {
    fromRawValue(context, tmp_key->second, result.key);
  }
}

static inline std::string toString(const RNCWebViewMenuItemsStruct &value) {
  return "[Object RNCWebViewMenuItemsStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<RNCWebViewMenuItemsStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    RNCWebViewMenuItemsStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}


struct RNCWebViewBasicAuthCredentialStruct {
  std::string username{};
  std::string password{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewBasicAuthCredentialStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_username = map.find("username");
  if (tmp_username != map.end()) {
    fromRawValue(context, tmp_username->second, result.username);
  }
  auto tmp_password = map.find("password");
  if (tmp_password != map.end()) {
    fromRawValue(context, tmp_password->second, result.password);
  }
}

static inline std::string toString(const RNCWebViewBasicAuthCredentialStruct &value) {
  return "[Object RNCWebViewBasicAuthCredentialStruct]";
}

struct RNCWebViewNewSourceHeadersStruct {
  std::string name{};
  std::string value{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewNewSourceHeadersStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_name = map.find("name");
  if (tmp_name != map.end()) {
    fromRawValue(context, tmp_name->second, result.name);
  }
  auto tmp_value = map.find("value");
  if (tmp_value != map.end()) {
    fromRawValue(context, tmp_value->second, result.value);
  }
}

static inline std::string toString(const RNCWebViewNewSourceHeadersStruct &value) {
  return "[Object RNCWebViewNewSourceHeadersStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<RNCWebViewNewSourceHeadersStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    RNCWebViewNewSourceHeadersStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}


struct RNCWebViewNewSourceStruct {
  std::string uri{};
  std::string method{};
  std::string body{};
  std::vector<RNCWebViewNewSourceHeadersStruct> headers{};
  std::string html{};
  std::string baseUrl{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCWebViewNewSourceStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_uri = map.find("uri");
  if (tmp_uri != map.end()) {
    fromRawValue(context, tmp_uri->second, result.uri);
  }
  auto tmp_method = map.find("method");
  if (tmp_method != map.end()) {
    fromRawValue(context, tmp_method->second, result.method);
  }
  auto tmp_body = map.find("body");
  if (tmp_body != map.end()) {
    fromRawValue(context, tmp_body->second, result.body);
  }
  auto tmp_headers = map.find("headers");
  if (tmp_headers != map.end()) {
    fromRawValue(context, tmp_headers->second, result.headers);
  }
  auto tmp_html = map.find("html");
  if (tmp_html != map.end()) {
    fromRawValue(context, tmp_html->second, result.html);
  }
  auto tmp_baseUrl = map.find("baseUrl");
  if (tmp_baseUrl != map.end()) {
    fromRawValue(context, tmp_baseUrl->second, result.baseUrl);
  }
}

static inline std::string toString(const RNCWebViewNewSourceStruct &value) {
  return "[Object RNCWebViewNewSourceStruct]";
}
class RNCWebViewProps final : public ViewProps {
 public:
  RNCWebViewProps() = default;
  RNCWebViewProps(const PropsParserContext& context, const RNCWebViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool allowFileAccess{false};
  bool allowsProtectedMedia{false};
  bool allowsFullscreenVideo{false};
  RNCWebViewAndroidLayerType androidLayerType{RNCWebViewAndroidLayerType::None};
  RNCWebViewCacheMode cacheMode{RNCWebViewCacheMode::LOAD_DEFAULT};
  bool domStorageEnabled{false};
  std::string downloadingMessage{};
  bool forceDarkOn{false};
  bool geolocationEnabled{false};
  std::string lackPermissionToDownloadMessage{};
  std::string messagingModuleName{};
  int minimumFontSize{0};
  RNCWebViewMixedContentMode mixedContentMode{RNCWebViewMixedContentMode::Never};
  bool nestedScrollEnabled{false};
  std::string overScrollMode{};
  bool saveFormDataDisabled{false};
  bool scalesPageToFit{true};
  bool setBuiltInZoomControls{true};
  bool setDisplayZoomControls{false};
  bool setSupportMultipleWindows{true};
  int textZoom{0};
  bool thirdPartyCookiesEnabled{true};
  bool hasOnScroll{false};
  std::string allowingReadAccessToURL{};
  bool allowsBackForwardNavigationGestures{false};
  bool allowsInlineMediaPlayback{false};
  bool allowsPictureInPictureMediaPlayback{false};
  bool allowsAirPlayForMediaPlayback{false};
  bool allowsLinkPreview{true};
  bool automaticallyAdjustContentInsets{true};
  bool autoManageStatusBarEnabled{true};
  bool bounces{true};
  RNCWebViewContentInsetStruct contentInset{};
  RNCWebViewContentInsetAdjustmentBehavior contentInsetAdjustmentBehavior{RNCWebViewContentInsetAdjustmentBehavior::Never};
  RNCWebViewContentMode contentMode{RNCWebViewContentMode::Recommended};
  RNCWebViewDataDetectorTypesMask dataDetectorTypes{static_cast<RNCWebViewDataDetectorTypesMask>(RNCWebViewDataDetectorTypes::PhoneNumber)};
  double decelerationRate{0.0};
  bool directionalLockEnabled{true};
  bool enableApplePay{false};
  bool hideKeyboardAccessoryView{false};
  bool keyboardDisplayRequiresUserAction{true};
  bool limitsNavigationsToAppBoundDomains{false};
  RNCWebViewMediaCapturePermissionGrantType mediaCapturePermissionGrantType{RNCWebViewMediaCapturePermissionGrantType::Prompt};
  bool pagingEnabled{false};
  bool pullToRefreshEnabled{false};
  bool refreshControlLightMode{false};
  bool scrollEnabled{true};
  bool sharedCookiesEnabled{false};
  bool textInteractionEnabled{true};
  bool useSharedProcessPool{true};
  std::vector<RNCWebViewMenuItemsStruct> menuItems{};
  std::vector<std::string> suppressMenuItems{};
  bool hasOnFileDownload{false};
  bool fraudulentWebsiteWarningEnabled{true};
  bool allowFileAccessFromFileURLs{false};
  bool allowUniversalAccessFromFileURLs{false};
  std::string applicationNameForUserAgent{};
  RNCWebViewBasicAuthCredentialStruct basicAuthCredential{};
  bool cacheEnabled{true};
  bool incognito{false};
  std::string injectedJavaScript{};
  std::string injectedJavaScriptBeforeContentLoaded{};
  bool injectedJavaScriptForMainFrameOnly{true};
  bool injectedJavaScriptBeforeContentLoadedForMainFrameOnly{true};
  bool javaScriptCanOpenWindowsAutomatically{false};
  bool javaScriptEnabled{true};
  bool webviewDebuggingEnabled{false};
  bool mediaPlaybackRequiresUserAction{true};
  bool messagingEnabled{false};
  bool hasOnOpenWindowEvent{false};
  bool showsHorizontalScrollIndicator{true};
  bool showsVerticalScrollIndicator{true};
  RNCWebViewIndicatorStyle indicatorStyle{RNCWebViewIndicatorStyle::Default};
  RNCWebViewNewSourceStruct newSource{};
  std::string userAgent{};
  std::string injectedJavaScriptObject{};
  bool paymentRequestEnabled{false};
};

} // namespace facebook::react
