/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeAsyncStorageModuleCxxSpecJSI : public TurboModule {
protected:
  NativeAsyncStorageModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void multiGet(jsi::Runtime &rt, jsi::Array keys, jsi::Function callback) = 0;
  virtual void multiSet(jsi::Runtime &rt, jsi::Array kvPairs, jsi::Function callback) = 0;
  virtual void multiRemove(jsi::Runtime &rt, jsi::Array keys, jsi::Function callback) = 0;
  virtual void multiMerge(jsi::Runtime &rt, jsi::Array kvPairs, jsi::Function callback) = 0;
  virtual void getAllKeys(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual void clear(jsi::Runtime &rt, jsi::Function callback) = 0;

};

template <typename T>
class JSI_EXPORT NativeAsyncStorageModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNCAsyncStorage";

protected:
  NativeAsyncStorageModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAsyncStorageModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAsyncStorageModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAsyncStorageModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void multiGet(jsi::Runtime &rt, jsi::Array keys, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::multiGet) == 3,
          "Expected multiGet(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::multiGet, jsInvoker_, instance_, std::move(keys), std::move(callback));
    }
    void multiSet(jsi::Runtime &rt, jsi::Array kvPairs, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::multiSet) == 3,
          "Expected multiSet(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::multiSet, jsInvoker_, instance_, std::move(kvPairs), std::move(callback));
    }
    void multiRemove(jsi::Runtime &rt, jsi::Array keys, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::multiRemove) == 3,
          "Expected multiRemove(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::multiRemove, jsInvoker_, instance_, std::move(keys), std::move(callback));
    }
    void multiMerge(jsi::Runtime &rt, jsi::Array kvPairs, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::multiMerge) == 3,
          "Expected multiMerge(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::multiMerge, jsInvoker_, instance_, std::move(kvPairs), std::move(callback));
    }
    void getAllKeys(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::getAllKeys) == 2,
          "Expected getAllKeys(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getAllKeys, jsInvoker_, instance_, std::move(callback));
    }
    void clear(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::clear) == 2,
          "Expected clear(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::clear, jsInvoker_, instance_, std::move(callback));
    }

  private:
    friend class NativeAsyncStorageModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
