{"name": "ExpoImage", "version": "2.4.0", "summary": "A cross-platform, performant image component for React Native and Expo with Web support", "description": "A cross-platform, performant image component for React Native and Expo with Web support", "license": "MIT", "authors": "650 Industries, Inc.", "homepage": "https://docs.expo.dev/versions/latest/sdk/image/", "platforms": {"ios": "15.1", "tvos": "15.1"}, "swift_versions": "5.4", "source": {"git": "https://github.com/expo/expo.git"}, "static_framework": true, "dependencies": {"ExpoModulesCore": [], "SDWebImage": ["~> 5.21.0"], "SDWebImageAVIFCoder": ["~> 0.11.0"], "SDWebImageSVGCoder": ["~> 1.7.0"], "SDWebImageWebPCoder": ["~> 0.14.6"], "libavif/libdav1d": []}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "source_files": "**/*.{h,m,swift}", "exclude_files": "Tests/", "testspecs": [{"name": "Tests", "test_type": "unit", "dependencies": {"ExpoModulesTestCore": []}, "source_files": "Tests/**/*.{m,swift}"}], "swift_version": "5.4"}