{"name": "React-performancetimeline", "version": "0.79.5", "summary": "React performance timeline utilities", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "source_files": "**/*.{cpp,h}", "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "header_dir": "react/performance/timeline", "exclude_files": "tests", "pod_target_xcconfig": {"CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/boost\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers\""}, "dependencies": {"React-featureflags": [], "React-jsinspectortracing": [], "React-timing": [], "React-cxxreact": [], "React-perflogger": [], "RCT-Folly": ["2024.11.18.00"]}}