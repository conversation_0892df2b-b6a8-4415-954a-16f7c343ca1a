{"name": "React-RCTFabric", "version": "0.79.5", "summary": "RCTFabric for React Native.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "source_files": "Fabric/**/*.{c,h,m,mm,S,cpp}", "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON><PERSON>Y_USE_LIBCPP=1 -DF<PERSON>LY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation -DRCT_NEW_ARCH_ENABLED=1", "exclude_files": ["**/tests/*", "**/android/*", "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON><PERSON>Y_USE_LIBCPP=1 -DF<PERSON>LY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation -DRCT_NEW_ARCH_ENABLED=1"], "header_dir": "React", "module_name": "RCTFabric", "weak_frameworks": "JavaScriptCore", "frameworks": "MobileCoreServices", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/ReactCommon\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/DoubleConversion\" \"$(PODS_ROOT)/fast_float/include\" \"$(PODS_ROOT)/fmt/include\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/Headers/Private/React-Core\" \"$(PODS_ROOT)/Headers/Private/Yoga\" \"$(PODS_ROOT)/Headers/Public/ReactCodegen\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage/React_FabricImage.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/imagemanager/platform/ios\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents/React_FabricComponents.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents/React_FabricComponents.framework/Headers/react/renderer/textlayoutmanager/platform/ios\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents/React_FabricComponents.framework/Headers/react/renderer/components/textinput/platform/ios\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager/React_ImageManager.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags/React_featureflags.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-utils/React_utils.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline/React_performancetimeline.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug/React_rendererdebug.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency/React_rendererconsistency.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler/React_runtimescheduler.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation/RCTAnimation.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss/React_renderercss.framework/Headers\"", "OTHER_CFLAGS": "$(inherited) -D<PERSON><PERSON><PERSON>Y_NO_CONFIG -DF<PERSON><PERSON>Y_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED=1", "CLANG_CXX_LANGUAGE_STANDARD": "c++20"}, "dependencies": {"React-Core": [], "React-RCTImage": [], "RCT-Folly/Fabric": ["2024.11.18.00"], "glog": [], "Yoga": [], "React-RCTText": [], "React-jsi": [], "React-FabricImage": [], "React-Fabric": [], "React-FabricComponents": [], "React-graphics": [], "React-ImageManager": [], "React-featureflags": [], "React-debug": [], "React-utils": [], "React-performancetimeline": [], "React-rendererdebug": [], "React-rendererconsistency": [], "React-runtimescheduler": [], "React-RCTAnimation": [], "React-jsinspector": [], "React-jsinspectortracing": [], "React-renderercss": [], "hermes-engine": [], "React-hermes": []}, "testspecs": [{"name": "Tests", "test_type": "unit", "source_files": "Tests/**/*.{mm}", "frameworks": "XCTest"}]}