{"name": "React-Core", "version": "0.79.5", "summary": "The core of React Native.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "resource_bundles": {"React-Core_privacy": "React/Resources/PrivacyInfo.xcprivacy"}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON><PERSON>Y_USE_LIBCPP=1 -DF<PERSON>LY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation -DUSE_HERMES=1", "header_dir": "React", "weak_frameworks": "JavaScriptCore", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "$(PODS_TARGET_SRCROOT)/ReactCommon $(PODS_ROOT)/boost $(PODS_ROOT)/DoubleConversion $(PODS_ROOT)/fast_float/include $(PODS_ROOT)/fmt/include $(PODS_ROOT)/RCT-Folly ${PODS_ROOT}/Headers/Public/FlipperKit $(PODS_ROOT)/Headers/Public/ReactCommon $(PODS_ROOT)/Headers/Public/React-hermes $(PODS_ROOT)/Headers/Public/hermes-engine \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation/RCTDeprecation.framework/Headers\"", "DEFINES_MODULE": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "RCT_METRO_PORT=${RCT_METRO_PORT}", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "FRAMEWORK_SEARCH_PATHS": "\"$(PODS_CONFIGURATION_BUILD_DIR)/React-hermes\""}, "user_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/Headers/Private/React-Core\""}, "default_subspecs": "<PERSON><PERSON><PERSON>", "dependencies": {"RCT-Folly": ["2024.11.18.00"], "React-cxxreact": [], "React-perflogger": [], "React-jsi": [], "React-jsiexecutor": [], "React-utils": [], "React-featureflags": [], "SocketRocket": ["0.7.1"], "React-runtimescheduler": [], "Yoga": [], "glog": [], "React-jsinspector": [], "React-jsitooling": [], "RCTDeprecation": [], "hermes-engine": [], "React-hermes": []}, "subspecs": [{"name": "<PERSON><PERSON><PERSON>", "source_files": "React/**/*.{c,h,m,mm,S,cpp}", "exclude_files": ["React/CoreModules/**/*", "React/DevSupport/**/*", "React/Fabric/**/*", "React/FBReactNativeSpec/**/*", "React/Tests/**/*", "React/Inspector/**/*", "React/Runtime/**/*", "React/CxxBridge/JSCExecutorFactory.{h,mm}"], "private_header_files": "React/Cxx*/*.h"}, {"name": "DevSupport", "source_files": ["React/DevSupport/*.{h,mm,m}", "React/Inspector/*.{h,mm,m}"], "dependencies": {"React-Core/Default": ["0.79.5"], "React-Core/RCTWebSocket": ["0.79.5"]}, "private_header_files": "React/Inspector/RCTCxx*.h"}, {"name": "RCTWebSocket", "source_files": "Libraries/WebSocket/*.{h,m}", "dependencies": {"React-Core/Default": ["0.79.5"]}}, {"name": "CoreModulesHeaders", "source_files": "React/CoreModules/**/*.h", "dependencies": {"React-Core/Default": []}}, {"name": "RCTActionSheetHeaders", "source_files": "Libraries/ActionSheetIOS/*.h", "dependencies": {"React-Core/Default": []}}, {"name": "RCTAnimationHeaders", "source_files": "Libraries/NativeAnimation/{Drivers/*,Nodes/*,*}.{h}", "dependencies": {"React-Core/Default": []}}, {"name": "RCTBlobHeaders", "source_files": "Libraries/Blob/{RCT<PERSON><PERSON>bManager,RCTFileReaderModule}.h", "dependencies": {"React-Core/Default": []}}, {"name": "RCTImageHeaders", "source_files": "Libraries/Image/*.h", "dependencies": {"React-Core/Default": []}}, {"name": "RCTLinkingHeaders", "source_files": "Libraries/LinkingIOS/*.h", "dependencies": {"React-Core/Default": []}}, {"name": "RCTNetworkHeaders", "source_files": "Libraries/Network/*.h", "dependencies": {"React-Core/Default": []}}, {"name": "RCTPushNotificationHeaders", "source_files": "Libraries/PushNotificationIOS/*.h", "dependencies": {"React-Core/Default": []}}, {"name": "RCTSettingsHeaders", "source_files": "Libraries/Settings/*.h", "dependencies": {"React-Core/Default": []}}, {"name": "RCTTextHeaders", "source_files": "Libraries/Text/**/*.h", "dependencies": {"React-Core/Default": []}}, {"name": "RCTVibrationHeaders", "source_files": "Libraries/Vibration/*.h", "dependencies": {"React-Core/Default": []}}]}