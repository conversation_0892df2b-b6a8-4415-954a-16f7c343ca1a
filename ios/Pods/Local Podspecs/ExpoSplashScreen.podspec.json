{"name": "ExpoSplashScreen", "version": "0.30.10", "summary": "Provides a module to allow keeping the native Splash Screen visible until you choose to hide it.", "description": "Provides a module to allow keeping the native Splash Screen visible until you choose to hide it.", "license": "MIT", "authors": "650 Industries, Inc.", "homepage": "https://docs.expo.dev/versions/latest/sdk/splash-screen/", "platforms": {"ios": "15.1", "tvos": "15.1"}, "source": {"git": "https://github.com/expo/expo.git"}, "static_framework": true, "dependencies": {"ExpoModulesCore": []}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "SWIFT_COMPILATION_MODE": "wholemodule", "OTHER_SWIFT_FLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED"}, "source_files": "**/*.{h,m,swift}"}