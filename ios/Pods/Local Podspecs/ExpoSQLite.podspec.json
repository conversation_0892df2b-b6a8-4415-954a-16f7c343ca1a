{"name": "ExpoSQLite", "version": "15.2.14", "summary": "Provides access to a database using SQLite (https://www.sqlite.org/). The database is persisted across restarts of your app.", "description": "Provides access to a database using SQLite (https://www.sqlite.org/). The database is persisted across restarts of your app.", "license": "MIT", "authors": "650 Industries, Inc.", "homepage": "https://docs.expo.dev/versions/latest/sdk/sqlite/", "platforms": {"ios": "15.1", "osx": "11.0"}, "source": {"git": "https://github.com/expo/expo.git"}, "dependencies": {"ExpoModulesCore": []}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "OTHER_CFLAGS": "$(inherited) -DHAVE_USLEEP=1 -DSQLITE_ENABLE_LOCKING_STYLE=0 -DSQLITE_ENABLE_BYTECODE_VTAB=1 -DSQLITE_TEMP_STORE=2 -DSQLITE_ENABLE_SESSION=1 -DSQLITE_ENABLE_PREUPDATE_HOOK=1 -DSQLITE_ENABLE_FTS4=1 -DSQLITE_ENABLE_FTS3_PARENTHESIS=1 -DSQLITE_ENABLE_FTS5=1", "OTHER_SWIFT_FLAGS": "$(inherited) -Xcc -DHAVE_USLEEP=1 -Xcc -DSQLITE_ENABLE_LOCKING_STYLE=0 -Xcc -DSQLITE_ENABLE_BYTECODE_VTAB=1 -Xcc -DSQLITE_TEMP_STORE=2 -Xcc -DS<PERSON><PERSON>E_ENABLE_SESSION=1 -Xcc -DSQLITE_ENABLE_PREUPDATE_HOOK=1 -Xcc -DSQLITE_ENABLE_FTS4=1 -Xcc -DSQLITE_ENABLE_FTS3_PARENTHESIS=1 -Xcc -DSQLITE_ENABLE_FTS5=1"}, "source_files": "**/*.{c,h,m,swift}", "exclude_files": ["libsql/**/*", "libsql.xcframework/**/*", "SQLiteModuleLibSQL.swift"], "ios": {"vendored_frameworks": []}}