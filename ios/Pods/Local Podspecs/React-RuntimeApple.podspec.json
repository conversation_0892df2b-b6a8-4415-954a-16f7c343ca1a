{"name": "React-RuntimeApple", "version": "0.79.5", "summary": "The React Native Runtime.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "source_files": "ReactCommon/*.{mm,h}", "header_dir": "ReactCommon", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "$(PODS_ROOT)/boost $(PODS_ROOT)/Headers/Private/React-Core $(PODS_TARGET_SRCROOT)/../../../.. $(PODS_TARGET_SRCROOT)/../../../../.. \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec/React_RCTFBReactNativeSpec.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes/React_RuntimeHermes.framework/Headers\"", "USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_PEDANTIC": "YES"}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"], "React-jsiexecutor": [], "React-cxxreact": [], "React-callinvoker": [], "React-runtimeexecutor": [], "React-runtimescheduler": [], "React-utils": [], "React-jsi": [], "React-Core/Default": [], "React-CoreModules": [], "React-NativeModulesApple": [], "React-RCTFabric": [], "React-RuntimeCore": [], "React-Mapbuffer": [], "React-jserrorhandler": [], "React-jsinspector": [], "React-featureflags": [], "React-jsitooling": [], "React-RCTFBReactNativeSpec": [], "hermes-engine": [], "React-RuntimeHermes": []}, "exclude_files": "ReactCommon/RCTJscInstance.{mm,h}"}