{"name": "ExpoHead", "version": "5.1.4", "summary": "Expo Router is a file-based router for React Native and web applications.", "description": "Expo Router is a file-based router for React Native and web applications.", "license": "MIT", "authors": "650 Industries, Inc.", "homepage": "https://docs.expo.dev/routing/introduction/", "platforms": {"ios": "15.1"}, "swift_versions": "5.4", "source": {"git": "https://github.com/expo/expo.git"}, "static_framework": true, "dependencies": {"ExpoModulesCore": []}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "SWIFT_COMPILATION_MODE": "wholemodule"}, "source_files": "**/*.{h,m,swift}", "swift_version": "5.4"}