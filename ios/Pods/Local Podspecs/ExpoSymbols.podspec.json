{"name": "ExpoSymbols", "version": "0.4.5", "summary": "Provides access to the SF Symbols library on iOS for React Native and Expo apps.", "description": "Provides access to the SF Symbols library on iOS for React Native and Expo apps.", "license": "MIT", "authors": "650 Industries, Inc.", "homepage": "https://docs.expo.dev/versions/latest/sdk/symbols/", "platforms": {"ios": "15.1", "tvos": "15.1"}, "swift_versions": "5.4", "source": {"git": "https://github.com/expo/expo.git"}, "static_framework": true, "dependencies": {"ExpoModulesCore": []}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "source_files": "**/*.{h,m,swift}", "swift_version": "5.4"}