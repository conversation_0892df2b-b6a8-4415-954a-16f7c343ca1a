{"name": "ExpoHaptics", "version": "14.1.4", "summary": "Provides access to the system's haptics engine on iOS, vibration effects on Android, and Web Vibration API on web.", "description": "Provides access to the system's haptics engine on iOS, vibration effects on Android, and Web Vibration API on web.", "license": "MIT", "authors": "650 Industries, Inc.", "homepage": "https://docs.expo.dev/versions/latest/sdk/haptics/", "platforms": {"ios": "15.1"}, "swift_versions": "5.4", "source": {"git": "https://github.com/expo/expo.git"}, "static_framework": true, "dependencies": {"ExpoModulesCore": []}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "SWIFT_COMPILATION_MODE": "wholemodule"}, "source_files": "**/*.{h,m,swift}", "swift_version": "5.4"}