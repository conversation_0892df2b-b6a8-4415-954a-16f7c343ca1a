{"name": "React-hermes", "version": "0.79.5", "summary": "Hermes engine for React Native", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "public_header_files": "executor/HermesExecutorFactory.h", "source_files": ["executor/*.{cpp,h}", "inspector-modern/chrome/*.{cpp,h}", "executor/HermesExecutorFactory.h"], "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"${PODS_ROOT}/hermes-engine/destroot/include\" \"$(PODS_TARGET_SRCROOT)/..\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/DoubleConversion\" \"$(PODS_ROOT)/fmt/include\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "DEFINES_MODULE": "YES"}, "header_dir": "reacthermes", "dependencies": {"React-cxxreact": ["0.79.5"], "React-jsiexecutor": ["0.79.5"], "React-jsinspector": [], "React-jsinspectortracing": [], "React-perflogger": ["0.79.5"], "RCT-Folly": ["2024.11.18.00"], "DoubleConversion": [], "fast_float": ["6.1.4"], "fmt": ["11.0.2"], "glog": [], "hermes-engine": [], "React-jsi": [], "React-runtimeexecutor": []}}