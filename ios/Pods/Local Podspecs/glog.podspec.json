{"name": "glog", "version": "0.3.5", "license": {"type": "Google", "file": "COPYING"}, "homepage": "https://github.com/google/glog", "summary": "Google logging module", "authors": "Google", "prepare_command": "#!/bin/bash\n# Copyright (c) Meta Platforms, Inc. and affiliates.\n#\n# This source code is licensed under the MIT license found in the\n# LICENSE file in the root directory of this source tree.\n\nset -e\n\nPLATFORM_NAME=\"${PLATFORM_NAME:-iphoneos}\"\nCURRENT_ARCH=\"${CURRENT_ARCH}\"\n\nif [ -z \"$CURRENT_ARCH\" ] || [ \"$CURRENT_ARCH\" == \"undefined_arch\" ]; then\n    # Xcode 10 beta sets CURRENT_ARCH to \"undefined_arch\", this leads to incorrect linker arg.\n    # it's better to rely on platform name as fallback because architecture differs between simulator and device\n\n    if [[ \"$PLATFORM_NAME\" == *\"simulator\"* ]]; then\n        CURRENT_ARCH=\"x86_64\"\n    else\n        CURRENT_ARCH=\"arm64\"\n    fi\nfi\n\n# @lint-ignore-every TXT2 Tab Literal\nif [ \"$CURRENT_ARCH\" == \"arm64\" ]; then\n    cat <<\\EOF >>fix_glog_0.3.5_apple_silicon.patch\ndiff --git a/config.sub b/config.sub\nindex 1761d8b..43fa2e8 100755\n--- a/config.sub\n+++ b/config.sub\n@@ -1096,6 +1096,9 @@ case $basic_machine in\n \t\tbasic_machine=z8k-unknown\n \t\tos=-sim\n \t\t;;\n+\tarm64-*)\n+\t\tbasic_machine=$(echo $basic_machine | sed 's/arm64/aarch64/')\n+\t\t;;\n \tnone)\n \t\tbasic_machine=none-none\n \t\tos=-none\nEOF\n\n    patch -p1 config.sub fix_glog_0.3.5_apple_silicon.patch\nfi\n\nXCRUN=\"$(which xcrun || true)\"\nif [ -n \"$XCRUN\" ]; then\n  export CC=\"$(xcrun -find -sdk $PLATFORM_NAME cc) -arch $CURRENT_ARCH -isysroot $(xcrun -sdk $PLATFORM_NAME --show-sdk-path)\"\n  export CXX=\"$CC\"\nelse\n  export CC=\"$CC:-$(which gcc)\"\n  export CXX=\"$CXX:-$(which g++ || true)\"\nfi\nexport CXX=\"$CXX:-$CC\"\n\n# Remove automake symlink if it exists\nif [ -h \"test-driver\" ]; then\n    rm test-driver\nfi\n\n# Manually disable gflags include to fix issue https://github.com/facebook/react-native/issues/28446\nsed -i.bak -e 's/\\@ac_cv_have_libgflags\\@/0/' src/glog/logging.h.in && rm src/glog/logging.h.in.bak\nsed -i.bak -e 's/HAVE_LIB_GFLAGS/HAVE_LIB_GFLAGS_DISABLED/' src/config.h.in && rm src/config.h.in.bak\n\n./configure --host arm-apple-darwin || true\n\ncat << EOF >> src/config.h\n/* Add in so we have Apple Target Conditionals */\n#ifdef __APPLE__\n#include <TargetConditionals.h>\n#include <Availability.h>\n#endif\n\n/* Special configuration for AppleTVOS */\n#if TARGET_OS_TV\n#undef HAVE_SYSCALL_H\n#undef HAVE_SYS_SYSCALL_H\n#undef OS_MACOSX\n/* NO_THREADS needed right now for Xcode 16 */\n#define NO_THREADS\n#endif\n\n/* Special configuration for ucontext */\n#undef HAVE_UCONTEXT_H\n#undef PC_FROM_UCONTEXT\n#if defined(__x86_64__)\n#define PC_FROM_UCONTEXT uc_mcontext->__ss.__rip\n#elif defined(__i386__)\n#define PC_FROM_UCONTEXT uc_mcontext->__ss.__eip\n#endif\nEOF\n\n# Prepare exported header include\nEXPORTED_INCLUDE_DIR=\"exported/glog\"\nmkdir -p exported/glog\ncp -f src/glog/log_severity.h \"$EXPORTED_INCLUDE_DIR/\"\ncp -f src/glog/logging.h \"$EXPORTED_INCLUDE_DIR/\"\ncp -f src/glog/raw_logging.h \"$EXPORTED_INCLUDE_DIR/\"\ncp -f src/glog/stl_logging.h \"$EXPORTED_INCLUDE_DIR/\"\ncp -f src/glog/vlog_is_on.h \"$EXPORTED_INCLUDE_DIR/\"", "source": {"git": "https://github.com/google/glog.git", "tag": "v0.3.5"}, "module_name": "glog", "header_dir": "glog", "source_files": ["src/glog/*.h", "src/demangle.cc", "src/logging.cc", "src/raw_logging.cc", "src/signalhandler.cc", "src/symbolize.cc", "src/utilities.cc", "src/vlog_is_on.cc"], "preserve_paths": ["src/*.h", "src/base/*.h"], "exclude_files": "src/windows/**/*", "compiler_flags": "-Wno-shorten-64-to-32", "resource_bundles": {"glog_privacy": "glog/PrivacyInfo.xcprivacy"}, "pod_target_xcconfig": {"USE_HEADERMAP": "NO", "HEADER_SEARCH_PATHS": "$(PODS_TARGET_SRCROOT)/src", "DEFINES_MODULE": "YES", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES"}, "platforms": {"ios": "15.1"}}