{"name": "React-RCTSettings", "version": "0.79.5", "summary": "A wrapper for NSUserDefaults, a persistent key-value store available only on iOS.", "homepage": "https://reactnative.dev/", "documentation_url": "https://reactnative.dev/docs/settings", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "compiler_flags": "-DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-nullability-completeness", "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "source_files": "*.{m,mm}", "preserve_paths": ["package.json", "LICENSE", "LICENSE-docs"], "header_dir": "RCTSettings", "pod_target_xcconfig": {"USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/RCT-Folly\" \"${PODS_ROOT}/Headers/Public/ReactCodegen/react/renderer/components\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec/React_RCTFBReactNativeSpec.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers/build/generated/ios\""}, "dependencies": {"RCT-Folly": ["2024.11.18.00"], "RCTTypeSafety": [], "React-jsi": [], "React-Core/RCTSettingsHeaders": [], "React-RCTFBReactNativeSpec": [], "ReactCommon": [], "React-NativeModulesApple": []}}