{"name": "React-RCTFBReactNativeSpec", "version": "0.79.5", "summary": "FBReactNativeSpec for React Native.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "source_files": "FBReactNativeSpec/**/*.{c,h,m,mm,S,cpp}", "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON><PERSON>Y_USE_LIBCPP=1 -DF<PERSON>LY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation -DRCT_NEW_ARCH_ENABLED=1", "header_dir": "FBReactNativeSpec", "module_name": "FBReactNativeSpec", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/RCT-Folly\" \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core\" \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/bridging\"", "OTHER_CFLAGS": "$(inherited) -D<PERSON><PERSON><PERSON>Y_NO_CONFIG -DF<PERSON><PERSON>Y_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED=1", "CLANG_CXX_LANGUAGE_STANDARD": "c++20"}, "dependencies": {"React-jsi": [], "React-jsiexecutor": [], "RCT-Folly": [], "RCTRequired": [], "RCTTypeSafety": [], "React-Core": [], "React-NativeModulesApple": [], "ReactCommon": [], "hermes-engine": [], "React-hermes": []}, "script_phases": [{"name": "[RN]Check FBReactNativeSpec", "execution_position": "before_compile", "always_out_of_date": "1", "script": "echo \"Checking whether Codegen has run...\"\nfbReactNativeSpecPath=\"$REACT_NATIVE_PATH/React/FBReactNativeSpec\"\n\nif [[ ! -d \"$fbReactNativeSpecPath\" ]]; then\n  echo 'error: Codegen did not run properly in your project. Please reinstall cocoapods with `bundle exec pod install`.'\n  exit 1\nfi\n"}]}