{"name": "RNGoogleMobileAds", "version": "15.4.0", "description": "React Native Google Mobile Ads is an easy way to monetize mobile apps with targeted, in-app advertising.", "summary": "React Native Google Mobile Ads is an easy way to monetize mobile apps with targeted, in-app advertising.", "homepage": "http://invertase.io/oss/react-native-google-mobile-ads", "license": "Apache-2.0", "authors": "Invertase Limited", "source": {"git": "https://github.com/invertase/react-native-google-mobile-ads.git", "tag": "v15.4.0"}, "social_media_url": "http://twitter.com/invertaseio", "platforms": {"ios": "12.0"}, "cocoapods_version": ">= 1.12.0", "source_files": "ios/**/*.{h,m,mm,swift}", "weak_frameworks": "AppTrackingTransparency", "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1 -D<PERSON><PERSON><PERSON><PERSON>_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DF<PERSON>LY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "dependencies": {"React-Core": [], "RCT-Folly": ["2024.11.18.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": [], "React-renderercss": [], "hermes-engine": [], "React-hermes": [], "GoogleUserMessagingPlatform": ["3.0.0"], "Google-Mobile-Ads-SDK": ["12.4.0"]}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "OTHER_SWIFT_FLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED"}, "static_framework": false}