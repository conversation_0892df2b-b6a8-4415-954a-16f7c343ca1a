{"name": "React-cxxreact", "version": "0.79.5", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "source_files": "*.{cpp,h}", "exclude_files": "SampleCxxModule.*", "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/DoubleConversion\" \"$(PODS_ROOT)/fas_float/include\" \"$(PODS_ROOT)/fmt/include\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-debug/React_debug.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimeexecutor/React_runtimeexecutor.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20"}, "header_dir": "cxxreact", "dependencies": {"boost": [], "DoubleConversion": [], "fast_float": ["6.1.4"], "fmt": ["11.0.2"], "RCT-Folly": ["2024.11.18.00"], "glog": [], "React-jsinspector": [], "React-jsinspectortracing": [], "React-callinvoker": ["0.79.5"], "React-runtimeexecutor": ["0.79.5"], "React-perflogger": ["0.79.5"], "React-jsi": ["0.79.5"], "React-logger": ["0.79.5"], "React-debug": ["0.79.5"], "React-timing": ["0.79.5"], "hermes-engine": []}, "resource_bundles": {"React-cxxreact_privacy": "PrivacyInfo.xcprivacy"}}