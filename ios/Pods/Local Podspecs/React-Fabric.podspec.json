{"name": "React-Fabric", "version": "0.79.5", "summary": "<PERSON><PERSON><PERSON> for React Native.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "source_files": "dummyFile.cpp", "pod_target_xcconfig": {"USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "DEFINES_MODULE": "YES", "HEADER_SEARCH_PATHS": "\"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug/React_rendererdebug.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios\""}, "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"], "React-jsiexecutor": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/core": [], "React-jsi": [], "React-logger": [], "glog": [], "DoubleConversion": [], "fast_float": ["6.1.4"], "fmt": ["11.0.2"], "React-Core": [], "React-debug": [], "React-featureflags": [], "React-utils": [], "React-runtimescheduler": [], "React-cxxreact": [], "React-rendererdebug": [], "React-graphics": [], "hermes-engine": [], "React-hermes": []}, "script_phases": [{"name": "[RN]Check rncore", "execution_position": "before_compile", "always_out_of_date": "1", "script": "echo \"Checking whether Codegen has run...\"\nrncorePath=\"$REACT_NATIVE_PATH/ReactCommon/react/renderer/components/rncore\"\n\nif [[ ! -d \"$rncorePath\" ]]; then\n  echo 'error: Codegen did not run properly in your project. Please reinstall cocoapods with `bundle exec pod install`.'\n  exit 1\nfi\n"}], "subspecs": [{"name": "animations", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/animations/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/animations/tests", "header_dir": "react/renderer/animations"}, {"name": "attributed<PERSON>ring", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/attributedstring/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/attributedstring/tests", "header_dir": "react/renderer/attributedstring"}, {"name": "core", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation", "source_files": "react/renderer/core/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/core/tests", "header_dir": "react/renderer/core", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_TARGET_SRCROOT)/ReactCommon\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/Headers/Private/Yoga\" \"$(PODS_TARGET_SRCROOT)\" \"$(PODS_ROOT)/DoubleConversion\" \"$(PODS_ROOT)/fmt/include\""}}, {"name": "componentregistry", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/componentregistry/*.{m,mm,cpp,h}", "header_dir": "react/renderer/componentregistry"}, {"name": "componentregistrynative", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/componentregistry/native/**/*.{m,mm,cpp,h}", "header_dir": "react/renderer/componentregistry/native"}, {"name": "components", "exclude_files": "react/renderer/components/scrollview/tests", "subspecs": [{"name": "root", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/components/root/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/components/root/tests", "header_dir": "react/renderer/components/root"}, {"name": "view", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"], "React-renderercss": [], "Yoga": []}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/components/view/**/*.{m,mm,cpp,h}", "exclude_files": ["react/renderer/components/view/tests", "react/renderer/components/view/platform/android", "react/renderer/components/view/platform/windows"], "header_dir": "react/renderer/components/view", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/Headers/Private/Yoga\""}}, {"name": "scrollview", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/components/scrollview/*.{m,mm,cpp,h}", "header_dir": "react/renderer/components/scrollview"}, {"name": "legacyviewmanagerinterop", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/components/legacyviewmanagerinterop/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/components/legacyviewmanagerinterop/tests", "header_dir": "react/renderer/components/legacyviewmanagerinterop", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/Headers/Private/React-Core\""}}]}, {"name": "dom", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"], "React-graphics": []}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/dom/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/dom/tests", "header_dir": "react/renderer/dom"}, {"name": "scheduler", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"], "React-performancetimeline": [], "React-Fabric/observers/events": []}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/scheduler/**/*.{m,mm,cpp,h}", "header_dir": "react/renderer/scheduler"}, {"name": "imagemanager", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/imagemanager/*.{m,mm,cpp,h}", "header_dir": "react/renderer/imagemanager"}, {"name": "mounting", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/mounting/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/mounting/tests", "header_dir": "react/renderer/mounting"}, {"name": "observers", "subspecs": [{"name": "events", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/observers/events/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/observers/events/tests", "header_dir": "react/renderer/observers/events"}]}, {"name": "templateprocessor", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/templateprocessor/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/templateprocessor/tests", "header_dir": "react/renderer/templateprocessor"}, {"name": "telemetry", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/telemetry/**/*.{m,mm,cpp,h}", "exclude_files": "react/renderer/telemetry/tests", "header_dir": "react/renderer/telemetry"}, {"name": "consistency", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/consistency/**/*.{m,mm,cpp,h}", "header_dir": "react/renderer/consistency"}, {"name": "<PERSON><PERSON><PERSON>", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"], "React-rendererconsistency": []}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/uimanager/*.{m,mm,cpp,h}", "header_dir": "react/renderer/uimanager", "subspecs": [{"name": "consistency", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/uimanager/consistency/*.{m,mm,cpp,h}", "header_dir": "react/renderer/uimanager/consistency"}]}, {"name": "leakchecker", "dependencies": {"RCT-Folly/Fabric": ["2024.11.18.00"]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "react/renderer/leakchecker/**/*.{cpp,h}", "exclude_files": "react/renderer/leakchecker/tests", "header_dir": "react/renderer/leakchecker", "pod_target_xcconfig": {"GCC_WARN_PEDANTIC": "YES"}}]}