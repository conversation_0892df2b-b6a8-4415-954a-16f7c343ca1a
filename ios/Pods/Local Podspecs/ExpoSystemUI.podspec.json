{"name": "ExpoSystemUI", "version": "5.0.10", "summary": "Interact with system UI elements", "description": "Interact with system UI elements", "license": "MIT", "authors": "650 Industries, Inc.", "homepage": "https://docs.expo.dev/versions/latest/sdk/system-ui", "platforms": {"ios": "15.1", "tvos": "15.1"}, "swift_versions": "5.4", "source": {"git": "https://github.com/expo/expo.git"}, "static_framework": true, "dependencies": {"ExpoModulesCore": []}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "resource_bundles": {"ExpoSystemUI_privacy": ["PrivacyInfo.xcprivacy"]}, "source_files": "ExpoSystemUI/**/*.{h,m,swift}", "swift_version": "5.4"}