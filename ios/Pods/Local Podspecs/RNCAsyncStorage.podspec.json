{"name": "RNCAsyncStorage", "version": "2.2.0", "summary": "Asynchronous, persistent, key-value storage system for React Native.", "license": "MIT", "authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/react-native-async-storage/async-storage#readme", "source": {"git": "https://github.com/react-native-async-storage/async-storage.git", "tag": "v2.2.0"}, "source_files": "ios/**/*.{h,m,mm}", "resource_bundles": {"RNCAsyncStorage_resources": "ios/PrivacyInfo.xcprivacy"}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/boost-for-react-native\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "OTHER_SWIFT_FLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED"}, "platforms": {"ios": "13.4", "tvos": "11.0", "osx": "10.15", "visionos": "1.0"}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON><PERSON>Y_MOBILE=1 -<PERSON><PERSON><PERSON><PERSON><PERSON>_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED=1  -DRCT_NEW_ARCH_ENABLED=1 -D<PERSON><PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DF<PERSON>LY_CFG_NO_COROUTINES=1 -DF<PERSON>LY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "dependencies": {"React-Core": [], "RCT-Folly": ["2024.11.18.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": [], "React-renderercss": [], "hermes-engine": [], "React-hermes": []}}