import BookmarkManager from '@/services/BookmarkManager';
import { BookmarkedQuestion, Question } from '@/services/DataManager';
import { useCallback, useEffect, useState } from 'react';

export interface UseBookmarksReturn {
  // Bookmark data
  bookmarks: BookmarkedQuestion[];
  loadingBookmarks: boolean;
  errorBookmarks: string | null;

  // Bookmark operations
  addBookmark: (question: Question, bcsTitle: string, fileUrl: string) => Promise<void>;
  removeBookmark: (question: Question, fileUrl: string) => Promise<void>;
  toggleBookmark: (question: Question, bcsTitle: string, fileUrl: string) => Promise<void>;
  isBookmarked: (question: Question, fileUrl: string) => boolean;

  // Search and filter
  searchBookmarks: (query: string) => Promise<BookmarkedQuestion[]>;
  getBookmarksBySubject: (subject: string) => Promise<BookmarkedQuestion[]>;
  getBookmarksByBCS: (bcsTitle: string) => Promise<BookmarkedQuestion[]>;

  // Metadata
  bookmarkSubjects: string[];
  bookmarkBCSTitles: string[];
  bookmarkStats: {
    total: number;
    bySubject: { [subject: string]: number };
    byBCS: { [bcsTitle: string]: number };
  };

  // Utility functions
  loadBookmarks: () => Promise<void>;
  refreshBookmarks: () => Promise<void>;
  clearAllBookmarks: () => Promise<void>;
  exportBookmarks: () => Promise<string>;
  importBookmarks: (jsonData: string, mergeWithExisting?: boolean) => Promise<void>;
}

export const useBookmarks = (): UseBookmarksReturn => {
  const [bookmarks, setBookmarks] = useState<BookmarkedQuestion[]>([]);
  const [loadingBookmarks, setLoadingBookmarks] = useState(false);
  const [errorBookmarks, setErrorBookmarks] = useState<string | null>(null);
  const [bookmarkSubjects, setBookmarkSubjects] = useState<string[]>(['All']);
  const [bookmarkBCSTitles, setBookmarkBCSTitles] = useState<string[]>(['All']);
  const [bookmarkStats, setBookmarkStats] = useState<{
    total: number;
    bySubject: { [subject: string]: number };
    byBCS: { [bcsTitle: string]: number };
  }>({ total: 0, bySubject: {}, byBCS: {} });

  const bookmarkManager = BookmarkManager.getInstance();

  // Load bookmarks from storage
  const loadBookmarks = useCallback(async () => {
    try {
      setLoadingBookmarks(true);
      setErrorBookmarks(null);

      const [
        bookmarksData,
        subjects,
        bcsTitles,
        stats
      ] = await Promise.all([
        bookmarkManager.getBookmarks(),
        bookmarkManager.getBookmarkSubjects(),
        bookmarkManager.getBookmarkBCSTitles(),
        bookmarkManager.getBookmarkStats()
      ]);

      console.log('📚 Loaded bookmarks:', {
        count: bookmarksData.length,
        subjects: subjects.length,
        bcsTitles: bcsTitles.length,
        bookmarks: bookmarksData
      });

      setBookmarks(bookmarksData);
      setBookmarkSubjects(subjects);
      setBookmarkBCSTitles(bcsTitles);
      setBookmarkStats(stats);

    } catch (error) {
      console.error('Error loading bookmarks:', error);
      setErrorBookmarks(error instanceof Error ? error.message : 'Failed to load bookmarks');
    } finally {
      setLoadingBookmarks(false);
    }
  }, [bookmarkManager]);

  // Refresh bookmarks (alias for loadBookmarks)
  const refreshBookmarks = useCallback(() => loadBookmarks(), [loadBookmarks]);

  // Add bookmark
  const addBookmark = useCallback(async (question: Question, bcsTitle: string, fileUrl: string) => {
    try {
      await bookmarkManager.addBookmark(question, bcsTitle, fileUrl);
      await refreshBookmarks(); // Refresh to update state
    } catch (error) {
      console.error('Error adding bookmark:', error);
      throw error;
    }
  }, [bookmarkManager, refreshBookmarks]);

  // Remove bookmark
  const removeBookmark = useCallback(async (question: Question, fileUrl: string) => {
    try {
      await bookmarkManager.removeBookmark(question, fileUrl);
      await refreshBookmarks(); // Refresh to update state
    } catch (error) {
      console.error('Error removing bookmark:', error);
      throw error;
    }
  }, [bookmarkManager, refreshBookmarks]);

  // Toggle bookmark (add if not bookmarked, remove if bookmarked)
  const toggleBookmark = useCallback(async (question: Question, bcsTitle: string, fileUrl: string) => {
    try {
      const isCurrentlyBookmarked = await bookmarkManager.isBookmarked(question, fileUrl);

      console.log('🔖 Toggling bookmark:', {
        questionNumber: question.question_number,
        bcsTitle,
        fileUrl,
        isCurrentlyBookmarked
      });

      if (isCurrentlyBookmarked) {
        console.log('➖ Removing bookmark');
        await removeBookmark(question, fileUrl);
      } else {
        console.log('➕ Adding bookmark');
        await addBookmark(question, bcsTitle, fileUrl);
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      throw error;
    }
  }, [bookmarkManager, addBookmark, removeBookmark]);

  // Check if question is bookmarked (using local state for performance)
  const isBookmarked = useCallback((question: Question, fileUrl: string): boolean => {
    const bookmarkId = `${fileUrl}_${question.question_number}_${question.question_text.slice(0, 50)}`.replace(/[^a-zA-Z0-9]/g, '_');
    return bookmarks.some(b => b.id === bookmarkId);
  }, [bookmarks]);

  // Search bookmarks
  const searchBookmarks = useCallback(async (query: string): Promise<BookmarkedQuestion[]> => {
    try {
      return await bookmarkManager.searchBookmarks(query);
    } catch (error) {
      console.error('Error searching bookmarks:', error);
      return [];
    }
  }, [bookmarkManager]);

  // Get bookmarks by subject
  const getBookmarksBySubject = useCallback(async (subject: string): Promise<BookmarkedQuestion[]> => {
    try {
      return await bookmarkManager.getBookmarksBySubject(subject);
    } catch (error) {
      console.error('Error filtering bookmarks by subject:', error);
      return [];
    }
  }, [bookmarkManager]);

  // Get bookmarks by BCS
  const getBookmarksByBCS = useCallback(async (bcsTitle: string): Promise<BookmarkedQuestion[]> => {
    try {
      return await bookmarkManager.getBookmarksByBCS(bcsTitle);
    } catch (error) {
      console.error('Error filtering bookmarks by BCS:', error);
      return [];
    }
  }, [bookmarkManager]);

  // Clear all bookmarks
  const clearAllBookmarks = useCallback(async () => {
    try {
      await bookmarkManager.clearAllBookmarks();
      await refreshBookmarks();
    } catch (error) {
      console.error('Error clearing bookmarks:', error);
      throw error;
    }
  }, [bookmarkManager, refreshBookmarks]);

  // Export bookmarks
  const exportBookmarks = useCallback(async (): Promise<string> => {
    try {
      return await bookmarkManager.exportBookmarks();
    } catch (error) {
      console.error('Error exporting bookmarks:', error);
      throw error;
    }
  }, [bookmarkManager]);

  // Import bookmarks
  const importBookmarks = useCallback(async (jsonData: string, mergeWithExisting = true) => {
    try {
      await bookmarkManager.importBookmarks(jsonData, mergeWithExisting);
      await refreshBookmarks();
    } catch (error) {
      console.error('Error importing bookmarks:', error);
      throw error;
    }
  }, [bookmarkManager, refreshBookmarks]);

  // Load bookmarks on mount
  useEffect(() => {
    loadBookmarks();
  }, [loadBookmarks]);

  return {
    // Bookmark data
    bookmarks,
    loadingBookmarks,
    errorBookmarks,

    // Bookmark operations
    addBookmark,
    removeBookmark,
    toggleBookmark,
    isBookmarked,

    // Search and filter
    searchBookmarks,
    getBookmarksBySubject,
    getBookmarksByBCS,

    // Metadata
    bookmarkSubjects,
    bookmarkBCSTitles,
    bookmarkStats,

    // Utility functions
    loadBookmarks,
    refreshBookmarks,
    clearAllBookmarks,
    exportBookmarks,
    importBookmarks,
  };
};

export default useBookmarks;
