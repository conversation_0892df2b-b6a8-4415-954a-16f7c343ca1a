import DataManager, { BCSSet, Question } from '@/services/DataManager';
import * as Network from 'expo-network';
import { useCallback, useEffect, useState } from 'react';

export interface UseDataManagerReturn {
  // BCS Sets
  bcsSets: BCSSet[];
  loadingBCSSets: boolean;
  errorBCSSets: string | null;
  loadBCSSets: (forceRefresh?: boolean) => Promise<void>;
  refreshBCSSets: () => Promise<void>;

  // Questions
  questions: Question[];
  loadingQuestions: boolean;
  errorQuestions: string | null;
  loadQuestions: (fileUrl: string, forceRefresh?: boolean) => Promise<void>;

  // Download Management
  downloadProgress: { [fileUrl: string]: number };
  downloadQuestion: (bcsSet: BCSSet) => Promise<void>;
  getDownloadStatus: (fileUrl: string) => Promise<string>;

  // Auto Download
  autoDownloadStatus: {
    isRunning: boolean;
    current: number;
    total: number;
    currentSet: string;
    completed: boolean;
    errors: string[];
  };
  startAutoDownload: () => Promise<void>;
  checkAutoDownloadStatus: () => Promise<void>;

  // Utility
  storageInfo: { totalSize: number; questionSets: number } | null;
  isOnline: boolean;
  clearCache: () => Promise<void>;
  refreshStorageInfo: () => Promise<void>;
}

export const useDataManager = (): UseDataManagerReturn => {
  const [bcsSets, setBcsSets] = useState<BCSSet[]>([]);
  const [loadingBCSSets, setLoadingBCSSets] = useState(false);
  const [errorBCSSets, setErrorBCSSets] = useState<string | null>(null);

  const [questions, setQuestions] = useState<Question[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  const [errorQuestions, setErrorQuestions] = useState<string | null>(null);

  const [downloadProgress, setDownloadProgress] = useState<{ [fileUrl: string]: number }>({});
  const [storageInfo, setStorageInfo] = useState<{ totalSize: number; questionSets: number } | null>(null);
  const [isOnline, setIsOnline] = useState(true);

  // Auto Download State
  const [autoDownloadStatus, setAutoDownloadStatus] = useState({
    isRunning: false,
    current: 0,
    total: 0,
    currentSet: '',
    completed: false,
    errors: [] as string[]
  });

  const dataManager = DataManager.getInstance();

  // Check online status
  const checkOnlineStatus = useCallback(async () => {
    const online = await dataManager.isOnline();
    const wasOnline = isOnline;
    setIsOnline(online);

    // Log status changes for debugging
    if (wasOnline !== online) {
      console.log(`🌐 Network status changed: ${online ? 'ONLINE' : 'OFFLINE'}`);
    }
  }, [dataManager, isOnline]);

  // Load BCS Sets
  const loadBCSSets = useCallback(async (forceRefresh = false) => {
    setLoadingBCSSets(true);
    setErrorBCSSets(null);

    try {
      const sets = await dataManager.getBCSSets(forceRefresh);

      // Update download status for each set
      const setsWithStatus = await Promise.all(
        sets.map(async (set) => ({
          ...set,
          downloadStatus: await dataManager.getDownloadStatus(set.file) as any
        }))
      );

      setBcsSets(setsWithStatus);
    } catch (error) {
      setErrorBCSSets(error instanceof Error ? error.message : 'Failed to load BCS sets');
    } finally {
      setLoadingBCSSets(false);
    }
  }, [dataManager]);

  // Refresh BCS Sets
  const refreshBCSSets = useCallback(async () => {
    await loadBCSSets(true);
  }, [loadBCSSets]);

  // Load Questions
  const loadQuestions = useCallback(async (fileUrl: string, forceRefresh = false) => {
    setLoadingQuestions(true);
    setErrorQuestions(null);

    try {
      const questionsData = await dataManager.getQuestions(fileUrl, forceRefresh);
      setQuestions(questionsData);
    } catch (error) {
      setErrorQuestions(error instanceof Error ? error.message : 'Failed to load questions');
    } finally {
      setLoadingQuestions(false);
    }
  }, [dataManager]);

  // Download Questions
  const downloadQuestion = useCallback(async (bcsSet: BCSSet) => {
    try {
      await dataManager.downloadQuestions(bcsSet, (progress) => {
        setDownloadProgress(prev => ({
          ...prev,
          [bcsSet.file]: progress
        }));
      });

      // Update the BCS set status
      setBcsSets(prev => prev.map(set =>
        set.file === bcsSet.file
          ? { ...set, downloadStatus: 'downloaded' }
          : set
      ));

      // Clear progress
      setDownloadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[bcsSet.file];
        return newProgress;
      });

    } catch (error) {
      // Update status to error
      setBcsSets(prev => prev.map(set =>
        set.file === bcsSet.file
          ? { ...set, downloadStatus: 'error' }
          : set
      ));

      throw error;
    }
  }, [dataManager]);

  // Get Download Status
  const getDownloadStatus = useCallback(async (fileUrl: string) => {
    return await dataManager.getDownloadStatus(fileUrl);
  }, [dataManager]);

  // Start Auto Download
  const startAutoDownload = useCallback(async () => {
    if (autoDownloadStatus.isRunning) {
      console.log('Auto-download already running');
      return;
    }

    // Check if auto-download should run
    const shouldRun = await dataManager.shouldAutoDownload();
    if (!shouldRun) {
      console.log('Auto-download conditions not met');
      return;
    }

    console.log('Starting auto-download...');

    setAutoDownloadStatus(prev => ({
      ...prev,
      isRunning: true,
      current: 0,
      total: 0,
      currentSet: 'Initializing...',
      completed: false,
      errors: []
    }));

    try {
      await dataManager.autoDownloadAllSets(
        // onProgress
        (current, total, currentSet) => {
          setAutoDownloadStatus(prev => ({
            ...prev,
            current,
            total,
            currentSet
          }));
        },
        // onComplete
        async () => {
          console.log('Auto-download completed successfully');
          await dataManager.markAutoDownloadComplete();

          setAutoDownloadStatus(prev => ({
            ...prev,
            isRunning: false,
            completed: true,
            currentSet: 'Completed'
          }));

          // Refresh BCS sets to show updated download status
          await loadBCSSets();
          await refreshStorageInfo();
        },
        // onError
        (error, setTitle) => {
          console.error(`Auto-download error for ${setTitle}:`, error);
          setAutoDownloadStatus(prev => ({
            ...prev,
            errors: [...prev.errors, `${setTitle}: ${error}`]
          }));
        }
      );
    } catch (error) {
      console.error('Auto-download failed:', error);
      setAutoDownloadStatus(prev => ({
        ...prev,
        isRunning: false,
        errors: [...prev.errors, `System error: ${error instanceof Error ? error.message : 'Unknown error'}`]
      }));
    }
  }, [dataManager, autoDownloadStatus.isRunning, loadBCSSets, refreshStorageInfo]);

  // Check Auto Download Status (for debugging)
  const checkAutoDownloadStatus = useCallback(async () => {
    const status = await dataManager.getAutoDownloadStatus();
    console.log('🔍 Auto-Download Debug Info:');
    console.log(`  Last Download: ${status.lastDownload || 'Never'}`);
    console.log(`  Hours Ago: ${status.hoursAgo === -1 ? 'N/A' : status.hoursAgo}`);
    console.log(`  Is Online: ${status.isOnline ? '✅' : '❌'}`);
    console.log(`  Should Run: ${status.shouldRun ? '✅' : '❌'}`);
  }, [dataManager]);

  // Clear Cache
  const clearCache = useCallback(async () => {
    await dataManager.clearCache();
    setBcsSets([]);
    setQuestions([]);
    setStorageInfo(null);
    await loadBCSSets();
  }, [dataManager, loadBCSSets]);

  // Refresh Storage Info
  const refreshStorageInfo = useCallback(async () => {
    const info = await dataManager.getStorageInfo();
    setStorageInfo(info);
  }, [dataManager]);

  // Initialize and start auto-download
  useEffect(() => {
    const initialize = async () => {
      await loadBCSSets();
      await refreshStorageInfo();
      await checkOnlineStatus();

      // Start auto-download after initialization
      setTimeout(() => {
        startAutoDownload();
      }, 2000); // Wait 2 seconds after app load
    };

    initialize();
  }, [loadBCSSets, refreshStorageInfo, checkOnlineStatus, startAutoDownload]);

  // Check online status periodically
  useEffect(() => {
    const interval = setInterval(checkOnlineStatus, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [checkOnlineStatus]);

  // Monitor network state changes and auto-resume downloads
  useEffect(() => {
    let networkStateSubscription: any;

    const setupNetworkMonitoring = async () => {
      try {
        // Listen for network state changes
        networkStateSubscription = Network.addNetworkStateListener(async (state) => {
          console.log('🌐 Network state changed:', state);

          const wasOffline = !isOnline;
          const isNowOnline = state.isConnected && state.isInternetReachable;

          setIsOnline(isNowOnline);

          // If we just came back online and auto-download is not running
          if (wasOffline && isNowOnline && !autoDownloadStatus.isRunning) {
            console.log('🔄 Network reconnected - checking for incomplete downloads...');

            // Check if there are incomplete downloads to resume
            const hasIncomplete = await dataManager.hasIncompleteDownloads();
            if (hasIncomplete) {
              console.log('📥 Resuming auto-download after network reconnection...');
              setTimeout(() => {
                startAutoDownload();
              }, 2000); // Wait 2 seconds before resuming
            }
          }
        });
      } catch (error) {
        console.error('Error setting up network monitoring:', error);
      }
    };

    setupNetworkMonitoring();

    return () => {
      if (networkStateSubscription) {
        networkStateSubscription.remove();
      }
    };
  }, [isOnline, autoDownloadStatus.isRunning, dataManager, startAutoDownload]);

  return {
    // BCS Sets
    bcsSets,
    loadingBCSSets,
    errorBCSSets,
    loadBCSSets,
    refreshBCSSets,

    // Questions
    questions,
    loadingQuestions,
    errorQuestions,
    loadQuestions,

    // Download Management
    downloadProgress,
    downloadQuestion,
    getDownloadStatus,

    // Auto Download
    autoDownloadStatus,
    startAutoDownload,
    checkAutoDownloadStatus,

    // Utility
    storageInfo,
    isOnline,
    clearCache,
    refreshStorageInfo,
  };
};

export default useDataManager;
